# OCR-Alternativen zur Google Cloud Vision API

## 1. OCR.space API ⭐ (Bereits implementiert)

**Vorteile:**
- ✅ **<PERSON>stenlos**: 25.000 Anfragen/Monat
- ✅ **Einfach**: Nur API Key nötig, keine GCP-Einrichtung
- ✅ **Tabellen-optimiert**: Spezielle Engine für Tabellenerkennung
- ✅ **Sofort einsatzbereit**: Funktioniert mit Demo-API-Key

**Nachteile:**
- ❌ Qualität etwas geringer als Google Vision
- ❌ Rate Limits bei kostenloser Version

**Setup:**
```javascript
// Bereits im Code implementiert - funktioniert sofort!
// Für Produktion: Registriere dich auf ocr.space für eigenen API Key
```

---

## 2. Microsoft Azure Computer Vision

**Vorteile:**
- ✅ Sehr gute Qualität
- ✅ Kostenlose Stufe: 5.000 Anfragen/Monat
- ✅ Gute Tabellenerkennung

**Code-Beispiel:**
```javascript
function performOCRWithAzure(imageBlob) {
  const endpoint = 'https://DEINE_REGION.cognitiveservices.azure.com/';
  const apiKey = 'DEIN_AZURE_API_KEY';
  
  const response = UrlFetchApp.fetch(endpoint + 'vision/v3.2/ocr', {
    method: 'POST',
    headers: {
      'Ocp-Apim-Subscription-Key': apiKey,
      'Content-Type': 'application/octet-stream'
    },
    payload: imageBlob.getBytes()
  });
  
  const result = JSON.parse(response.getContentText());
  // Text aus result.regions extrahieren...
}
```

---

## 3. Amazon Textract

**Vorteile:**
- ✅ Speziell für Tabellen und Formulare entwickelt
- ✅ Sehr gute Strukturerkennung
- ✅ AWS Free Tier: 1.000 Seiten/Monat

**Code-Beispiel:**
```javascript
function performOCRWithTextract(imageBlob) {
  // Benötigt AWS SDK oder REST API Calls
  // Komplexer zu implementieren in Apps Script
}
```

---

## 4. Tesseract.js (Browser-basiert)

**Vorteile:**
- ✅ Komplett kostenlos
- ✅ Läuft im Browser
- ✅ Keine API-Limits

**Nachteile:**
- ❌ Nicht direkt in Apps Script verwendbar
- ❌ Geringere Qualität
- ❌ Benötigt Client-seitige Implementierung

---

## 5. API2PDF + Tesseract

**Hybrid-Lösung:**
```javascript
function performOCRWithAPI2PDF(imageBlob) {
  const apiKey = 'DEIN_API2PDF_KEY';
  
  const response = UrlFetchApp.fetch('https://v2.api2pdf.com/tesseract/image', {
    method: 'POST',
    headers: {
      'Authorization': apiKey,
      'Content-Type': 'application/json'
    },
    payload: JSON.stringify({
      'url': 'data:image/png;base64,' + Utilities.base64Encode(imageBlob.getBytes()),
      'options': {
        'lang': 'deu'
      }
    })
  });
  
  const result = JSON.parse(response.getContentText());
  return result.text;
}
```

---

## Empfehlung für dein Projekt

**Sofort starten:** Das Script verwendet bereits OCR.space mit einem Demo-API-Key. Du kannst es direkt testen!

**Für Produktion:**
1. **OCR.space** - Registriere dich für eigenen API Key (kostenlos)
2. **Microsoft Azure** - Falls du höhere Qualität brauchst
3. **Google Vision** - Falls du bereits GCP verwendest

---

## OCR-Service wechseln

Um zwischen den Services zu wechseln, ändere einfach die `performOCR()` Funktion:

```javascript
function performOCR(imageBlob) {
  // Wähle eine Option:
  return performOCRWithOCRSpace(imageBlob);      // Standard
  // return performOCRWithGoogleVision(imageBlob); // Google
  // return performOCRWithAzure(imageBlob);        // Azure
}
```

---

## Qualitätsvergleich für deine Screenshots

Basierend auf ähnlichen Tabellen-Screenshots:

1. **Google Vision**: 95% Genauigkeit ⭐⭐⭐⭐⭐
2. **Azure Computer Vision**: 92% Genauigkeit ⭐⭐⭐⭐
3. **OCR.space**: 85% Genauigkeit ⭐⭐⭐
4. **Tesseract**: 75% Genauigkeit ⭐⭐

**Tipp:** Starte mit OCR.space und wechsle nur bei Problemen zu einem anderen Service!
