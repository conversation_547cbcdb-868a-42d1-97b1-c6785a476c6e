#!/usr/bin/env python3
"""
Einfaches Setup-Script für Michael's Screenshot Extractor
Prüft Installation und hilft beim Setup
"""

import os
import sys
import subprocess

def check_python():
    """Prüft Python-Installation"""
    print("🐍 Python-Version prüfen...")
    print(f"   Python: {sys.version}")
    
    if sys.version_info < (3, 7):
        print("❌ Python 3.7+ erforderlich")
        return False
    else:
        print("✅ Python-Version OK")
        return True

def check_packages():
    """Prüft ob alle Pakete installiert sind"""
    required_packages = [
        'pytesseract',
        'PIL',  # Pillow
        'pandas', 
        'openpyxl',
        'requests'
    ]
    
    missing_packages = []
    
    print("\n📦 Pakete prüfen...")
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
                print(f"   ✅ Pillow (PIL)")
            else:
                __import__(package)
                print(f"   ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            display_name = 'pillow' if package == 'PIL' else package
            print(f"   ❌ {display_name}")
    
    return missing_packages

def install_packages(missing_packages):
    """Installiert fehlende Pakete"""
    if not missing_packages:
        return True
    
    print(f"\n🔧 Installiere fehlende Pakete...")
    
    # Paket-Namen für pip anpassen
    pip_packages = []
    for pkg in missing_packages:
        if pkg == 'PIL':
            pip_packages.append('pillow')
        else:
            pip_packages.append(pkg)
    
    try:
        cmd = [sys.executable, '-m', 'pip', 'install'] + pip_packages
        print(f"   Führe aus: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Pakete erfolgreich installiert")
            return True
        else:
            print(f"❌ Fehler bei Installation: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Fehler bei Installation: {e}")
        return False

def check_files():
    """Prüft ob alle Script-Dateien vorhanden sind"""
    required_files = [
        'screenshot_kpi_extractor.py',
        'requirements.txt'
    ]
    
    print(f"\n📄 Script-Dateien prüfen...")
    print(f"   Aktuelles Verzeichnis: {os.getcwd()}")
    
    missing_files = []
    all_files = os.listdir('.')
    
    for file in required_files:
        if file in all_files:
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file}")
            missing_files.append(file)
    
    print(f"   📁 Alle Python-Dateien: {[f for f in all_files if f.endswith('.py')]}")
    
    return missing_files

def check_screenshots_folder():
    """Prüft Screenshots-Ordner"""
    screenshots_path = r'C:\Users\<USER>\Documents\python\screenshots'
    
    print(f"\n📸 Screenshots-Ordner prüfen...")
    print(f"   Pfad: {screenshots_path}")
    
    if os.path.exists(screenshots_path):
        png_files = [f for f in os.listdir(screenshots_path) if f.lower().endswith('.png')]
        print(f"   ✅ Ordner existiert")
        print(f"   📸 PNG-Dateien: {len(png_files)}")
        if png_files:
            print(f"   📋 Beispiele: {png_files[:3]}")
        return True, len(png_files)
    else:
        print(f"   ❌ Ordner existiert nicht")
        return False, 0

def create_screenshots_folder():
    """Erstellt Screenshots-Ordner"""
    screenshots_path = r'C:\Users\<USER>\Documents\python\screenshots'
    
    try:
        os.makedirs(screenshots_path, exist_ok=True)
        print(f"✅ Screenshots-Ordner erstellt: {screenshots_path}")
        return True
    except Exception as e:
        print(f"❌ Fehler beim Erstellen: {e}")
        return False

def run_test():
    """Führt einen Test-Lauf aus"""
    print(f"\n🧪 Test-Lauf...")
    
    try:
        # Versuche das Hauptscript zu importieren
        import screenshot_kpi_extractor
        print("✅ Hauptscript erfolgreich importiert")
        
        # Teste OCR.space (ohne echte Bilder)
        extractor = screenshot_kpi_extractor.ScreenshotKPIExtractor({
            'ocr_method': 'ocr_space',
            'log_level': 'ERROR'  # Weniger Output
        })
        print("✅ Extractor erfolgreich erstellt")
        
        return True
        
    except Exception as e:
        print(f"❌ Test fehlgeschlagen: {e}")
        return False

def main():
    print("🚀 Michael's Screenshot Extractor - Setup")
    print("=" * 60)
    
    # 1. Python prüfen
    if not check_python():
        print("\n❌ Setup abgebrochen - Python-Problem")
        return
    
    # 2. Pakete prüfen
    missing_packages = check_packages()
    
    if missing_packages:
        print(f"\n⚠️  Fehlende Pakete: {missing_packages}")
        response = input("Automatisch installieren? (j/n): ").lower().strip()
        
        if response in ['j', 'ja', 'y', 'yes']:
            if not install_packages(missing_packages):
                print("\n❌ Setup abgebrochen - Paket-Installation fehlgeschlagen")
                return
        else:
            print("\n💡 Installiere manuell mit: pip install pytesseract pillow pandas openpyxl requests")
            return
    
    # 3. Dateien prüfen
    missing_files = check_files()
    if missing_files:
        print(f"\n⚠️  Fehlende Dateien: {missing_files}")
        print("💡 Stelle sicher, dass alle Script-Dateien im gleichen Ordner sind")
    
    # 4. Screenshots-Ordner prüfen
    folder_exists, png_count = check_screenshots_folder()
    if not folder_exists:
        response = input("\nScreenshots-Ordner erstellen? (j/n): ").lower().strip()
        if response in ['j', 'ja', 'y', 'yes']:
            create_screenshots_folder()
    
    # 5. Test-Lauf
    if not missing_files:
        if run_test():
            print("\n🎉 Setup erfolgreich!")
            
            if png_count > 0:
                print(f"\n🚀 Bereit zum Starten!")
                print(f"   Führe aus: python screenshot_kpi_extractor.py")
                print(f"   Oder: python start_michael.py")
            else:
                print(f"\n📸 Lege Screenshots in den Ordner und starte dann:")
                print(f"   python screenshot_kpi_extractor.py")
        else:
            print(f"\n⚠️  Setup teilweise erfolgreich, aber Test fehlgeschlagen")
    else:
        print(f"\n⚠️  Setup unvollständig - fehlende Dateien")
    
    print(f"\n📋 Nächste Schritte:")
    print(f"   1. Screenshots in Ordner legen")
    print(f"   2. python screenshot_kpi_extractor.py ausführen")
    print(f"   3. michael_kpi_data.xlsx öffnen")

if __name__ == "__main__":
    main()
