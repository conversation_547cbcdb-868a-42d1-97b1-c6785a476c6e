# Screenshot KPI Data Extractor (Python)

Extrahiert KPI-Daten aus Schichtplanungs-Screenshots und speichert sie in Excel/CSV.

## 🚀 Schnellstart

```bash
# 1. Pakete installieren
pip install -r requirements.txt

# 2. Demo ausführen
python demo_kpi_extractor.py

# 3. Echte Screenshots verarbeiten
python screenshot_kpi_extractor.py /pfad/zu/screenshots
```

## 📋 Was wird extrahiert

Aus jedem Screenshot:
- **Location**: 3-Zeichen Standort-Kürzel (z.B. "AAH")
- **Planning Week**: Planungswoche (z.B. 35, 36)
- **Expected Delta**: Erwartete Delta in neg. Stundensalden
- **Shifts Unable to Assign**: Anzahl nicht zuweisbarer Schichten
- **Trips Below Forecast**: Anzahl Fahrten unter Prognose
- **Runners Submitted Availabilities**: Anzahl/Prozent eingereichte Verfügbarkeiten

## 🛠️ Installation

### Option 1: Einfach (mit OCR.space API)
```bash
pip install -r requirements.txt
# Fertig! Verwendet automatisch OCR.space (kostenlos)
```

### Option 2: Mit Tesseract (bessere Qualität)
```bash
# 1. Python Pakete
pip install -r requirements.txt

# 2. Tesseract OCR installieren
# Windows: https://github.com/UB-Mannheim/tesseract/wiki
# Mac: brew install tesseract
# Linux: sudo apt-get install tesseract-ocr
```

## 📖 Verwendung

### Kommandozeile

```bash
# Einfach
python screenshot_kpi_extractor.py /pfad/zu/screenshots

# Mit Optionen
python screenshot_kpi_extractor.py /pfad/zu/screenshots \
    --output meine_daten.xlsx \
    --ocr ocr_space \
    --verbose

# Hilfe anzeigen
python screenshot_kpi_extractor.py --help
```

### Python Code

```python
from screenshot_kpi_extractor import ScreenshotKPIExtractor

# Extractor erstellen
extractor = ScreenshotKPIExtractor()

# Screenshots verarbeiten
data = extractor.process_directory('/pfad/zu/screenshots')

# Daten speichern
extractor.save_data(data, 'output.xlsx')
```

### Konfiguration

```python
config = {
    'ocr_method': 'tesseract',  # oder 'ocr_space'
    'ocr_space_api_key': 'dein_api_key',  # für OCR.space
    'tesseract_path': None,  # automatisch oder manuell setzen
    'output_format': 'excel',  # oder 'csv'
    'log_level': 'INFO'
}

extractor = ScreenshotKPIExtractor(config)
```

## 🔧 OCR-Optionen

### 1. OCR.space API (Standard)
- ✅ **Kostenlos**: 25.000 Anfragen/Monat
- ✅ **Einfach**: Keine Installation nötig
- ✅ **Sofort einsatzbereit**

```bash
python screenshot_kpi_extractor.py screenshots/ --ocr ocr_space
```

### 2. Tesseract OCR
- ✅ **Offline**: Keine API-Limits
- ✅ **Bessere Qualität** bei guten Bildern
- ❌ **Installation nötig**

```bash
python screenshot_kpi_extractor.py screenshots/ --ocr tesseract
```

## 📊 Ausgabe

Das Script erstellt eine Excel-Datei mit folgenden Spalten:

| Spalte | Beschreibung | Beispiel |
|--------|--------------|----------|
| filename | Screenshot-Dateiname | `week35_screenshot.png` |
| location | Standort-Kürzel | `AAH` |
| planning_week | Planungswoche | `35` |
| expected_delta | Erwartete Delta | `-15` |
| shifts_unable_to_assign | Nicht zuweisbare Schichten | `0` |
| trips_below_forecast | Fahrten unter Prognose | `0` |
| runners_submitted_availabilities | Eingereichte Verfügbarkeiten | `71` |
| processed_date | Verarbeitungsdatum | `2025-01-15 14:30:00` |

## 🎯 Beispiele

### Beispiel 1: Einzelner Ordner
```bash
python screenshot_kpi_extractor.py "C:\Screenshots\Week35"
```

### Beispiel 2: Mit eigener Ausgabedatei
```bash
python screenshot_kpi_extractor.py screenshots/ -o weekly_kpis.xlsx
```

### Beispiel 3: CSV-Ausgabe
```bash
python screenshot_kpi_extractor.py screenshots/ -o data.csv
```

### Beispiel 4: Verbose Logging
```bash
python screenshot_kpi_extractor.py screenshots/ --verbose
```

## 🔍 Troubleshooting

### Problem: "Tesseract nicht gefunden"
**Lösung**: 
```python
# In der Konfiguration den Pfad setzen
config = {
    'tesseract_path': r'C:\Program Files\Tesseract-OCR\tesseract.exe'
}
```

### Problem: "Keine Daten extrahiert"
**Lösungen**:
1. Bildqualität prüfen (sollte scharf und gut lesbar sein)
2. OCR-Methode wechseln: `--ocr ocr_space`
3. Verbose Logging aktivieren: `--verbose`
4. Demo ausführen: `python demo_kpi_extractor.py`

### Problem: "OCR.space API Limit erreicht"
**Lösungen**:
1. Eigenen API Key registrieren auf [ocr.space](https://ocr.space/ocrapi)
2. Zu Tesseract wechseln: `--ocr tesseract`

## 🆚 Vergleich: Python vs Google Apps Script

| Feature | Python Script | Google Apps Script |
|---------|---------------|-------------------|
| **Setup** | Lokal installieren | Browser-basiert |
| **OCR-Optionen** | Tesseract + OCR.space | Google Vision + OCR.space |
| **Offline** | ✅ Mit Tesseract | ❌ |
| **Geschwindigkeit** | ⚡ Sehr schnell | 🐌 Langsamer |
| **Ausgabe** | Excel/CSV lokal | Google Sheets |
| **Automatisierung** | Cron/Task Scheduler | Google Triggers |
| **Kosten** | Kostenlos (Tesseract) | API-Kosten |

## 💡 Tipps

1. **Bildqualität**: Screenshots sollten scharf und gut lesbar sein
2. **Batch-Verarbeitung**: Das Script kann hunderte Bilder auf einmal verarbeiten
3. **Automatisierung**: Mit Cron (Linux/Mac) oder Task Scheduler (Windows) automatisieren
4. **Backup**: Originale Screenshots aufbewahren für Nachbearbeitung

## 🔄 Updates

Das Script kann einfach erweitert werden für:
- Weitere KPI-Werte
- Andere Screenshot-Formate
- Zusätzliche OCR-Services
- Datenbank-Integration
