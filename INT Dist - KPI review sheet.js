const REPORTING_ID = "1trjbOiLgOzrzv57AAomOfXjFQOmWsRS3cT_tvdRzdhg";
const OPS_REPORTING = "1MHbY6Oz-SiFloV5re7FSHDNqDf4WTMbKtGN1oqbrPcg";
const AVG_TENURE_ID = "1VnoCjKzzHnPYYg2Tjr0VnzGhT6eT-4J0ZzA1zOCE79w";

function fetchKPIs() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const dataSheet = ss.getSheetByName("Data");

  const mergedMap = new Map();

  const slotClosingsMap = fetchSlotClosings();
  console.log(`SlotClosings: ${slotClosingsMap.size} entries, first entry length: ${slotClosingsMap.size > 0 ? Array.from(slotClosingsMap.values())[0].length : 'N/A'}`);
  for (const [key, value] of slotClosingsMap.entries()) {
    mergedMap.set(key, value);
  }

  const hourBalancesMap = fetchHourBalances();
  console.log(`HourBalances: ${hourBalancesMap.size} entries, first entry length: ${hourBalancesMap.size > 0 ? Array.from(hourBalancesMap.values())[0].length : 'N/A'}`);
  for (const [key, value] of hourBalancesMap.entries()) {
    if (!mergedMap.has(key)) {
      mergedMap.set(key, value);
    } else {
      const existing = mergedMap.get(key);
      // Runner Hour Balances (7 columns: 5-11)
      existing[5] = value[5];   // Cumulative balances
      existing[6] = value[6];   // Cumulative neg. balances
      existing[7] = value[7];   // Cumulative pos. balances
      existing[8] = value[8];   // Below neg. threshold
      existing[9] = value[9];   // Above pos. threshold
      existing[10] = value[10]; // People below neg. threshold
      existing[11] = value[11]; // People above pos. threshold
      // RunnerPlus Hour Balances (7 columns: 12-18)
      existing[12] = value[12]; // R+ Cumulative balances
      existing[13] = value[13]; // R+ Cumulative neg. balances
      existing[14] = value[14]; // R+ Cumulative pos. balances
      existing[15] = value[15]; // R+ Below neg. threshold
      existing[16] = value[16]; // R+ Above pos. threshold
      existing[17] = value[17]; // R+ People below neg. threshold
      existing[18] = value[18]; // R+ People above pos. threshold
    }
  }

  const retentionMap = fetchRetention();
  console.log(`Retention: ${retentionMap.size} entries, first entry length: ${retentionMap.size > 0 ? Array.from(retentionMap.values())[0].length : 'N/A'}`);
  for (const [key, value] of retentionMap.entries()) {
    if (!mergedMap.has(key)) {
      mergedMap.set(key, value);
    } else {
      const existing = mergedMap.get(key);
      // Runner Retention (3 columns: 19-21)
      existing[19] = value[19]; // R 12 week pool retention
      existing[20] = value[20]; // R 8 week pool retention
      existing[21] = value[21]; // R 8 week hire retention
    }
  }

  const avgTenureMap = fetchAvgTenure(mergedMap);
  for (const [key, tenureValue] of avgTenureMap.entries()) {
    if (mergedMap.has(key)) {
      const existing = mergedMap.get(key);
      existing[22] = tenureValue; // Avg. Tenure (d) in column W (index 22)
    }
  }

  const cancelsMap = fetchCancels();
  console.log(`Cancels: ${cancelsMap.size} entries, first entry length: ${cancelsMap.size > 0 ? Array.from(cancelsMap.values())[0].length : 'N/A'}`);
  for (const [key, value] of cancelsMap.entries()) {
    if (!mergedMap.has(key)) {
      mergedMap.set(key, value);
    } else {
      const existing = mergedMap.get(key);
      existing[23] = value[23]; // Cancelled deliveries in column X (index 23)
      existing[24] = value[24]; // Dist cancelled deliveries in column Y (index 24)
    }
  }

  const result = Array.from(mergedMap.values());
  result.sort((a, b) => parseInt(a[2], 10) - parseInt(b[2], 10));

  dataSheet.clearContents();
  const headers = [
    "Country", "Hub", "Week",
    "Sum trips", "Weighted slot closing hrs.",
    // Runner Hour Balances (7 columns)
    "Cumulative balances", "Cumulative neg. balances", "Cumulative pos. balances",
    "Hrs. below neg. threshold", "Hrs. above pos. threshold",
    "People below neg. threshold", "People above pos. threshold",
    // RunnerPlus Hour Balances (7 columns)
    "R+ Cumulative balances", "R+ Cumulative neg. balances", "R+ Cumulative pos. balances",
    "R+ Hrs. below neg. threshold", "R+ Hrs. above pos. threshold",
    "R+ People below neg. threshold", "R+ People above pos. threshold",
    // Runner Retention (3 columns)
    "R 12 week pool retention", "R 8 week pool retention", "R 8 week hire retention",
    // Other metrics
    "Avg. Tenure (d)", "Cancelled deliveries", "Dist cancelled deliveries"
  ];
  dataSheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  if (result.length > 0) {
    // Debug: Check array lengths
    console.log(`Headers length: ${headers.length}`);
    console.log(`First result row length: ${result[0].length}`);
    console.log(`Expected: 25 columns, Got: ${result[0].length} columns`);

    // Ensure all rows have exactly 25 columns
    const normalizedResult = result.map(row => {
      if (row.length > headers.length) {
        return row.slice(0, headers.length); // Trim excess columns
      } else if (row.length < headers.length) {
        // Pad with nulls if too short
        const paddedRow = [...row];
        while (paddedRow.length < headers.length) {
          paddedRow.push(null);
        }
        return paddedRow;
      }
      return row;
    });

    dataSheet.getRange(2, 1, normalizedResult.length, headers.length).setValues(normalizedResult);
  }
}

function fetchSlotClosings() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName("Config");
  const reportingSheet = SpreadsheetApp.openById(REPORTING_ID);
  const sourceSheet = reportingSheet.getSheetByName("iDWH");

  const configData = configSheet.getRange(2, 1, configSheet.getLastRow() - 1, 2).getValues();
  const sourceValues = sourceSheet.getDataRange().getValues();

  const aggregationMap = new Map();

  configData.forEach(([country, location]) => {
    if (!country || !location) return;

    sourceValues.forEach(row => {
      const rowCountry = row[3];
      const rowLocation = row[4];

      if (rowCountry === country && rowLocation === location) {
        const week = row[7];
        const rawM = row[9];
        const rawAR = row[43];
        const sumDeliveries = rawM === "" ? null : parseFloat(rawM);
        const slotClosingHrs = rawAR === "" ? null : parseFloat(rawAR);

        if (sumDeliveries !== null && slotClosingHrs !== null) {
          const key = `${rowCountry}_${rowLocation}_${week}`;

          if (!aggregationMap.has(key)) {
            aggregationMap.set(key, { country: rowCountry, location: rowLocation, week, mSum: 0, weightedArSum: 0 });
          }

          const entry = aggregationMap.get(key);
          entry.mSum += sumDeliveries;
          entry.weightedArSum += sumDeliveries * slotClosingHrs;
        }
      }
    });
  });

  const resultMap = new Map();
  for (const [key, entry] of aggregationMap.entries()) {
    const weightedAr = entry.mSum > 0 ? entry.weightedArSum / entry.mSum : null;
    const row = [entry.country, entry.location, entry.week, entry.mSum, weightedAr,
      null, null, null, null, null, null, null, // Runner Hour Balances (7 columns)
      null, null, null, null, null, null, null, // RunnerPlus Hour Balances (7 columns)
      null, null, null, // Runner Retention (3 columns)
      null, // Avg. Tenure
      null, null]; // Cancelled deliveries (2 columns)
    resultMap.set(key, row);
  }
  return resultMap;
}

function fetchHourBalances() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName("Config");
  const reportingSheet = SpreadsheetApp.openById(OPS_REPORTING);
  const sourceSheet = reportingSheet.getSheetByName("iTIMEw");

  const configData = configSheet.getRange(2, 1, configSheet.getLastRow() - 1, 2).getValues();
  const sourceValues = sourceSheet.getDataRange().getValues();

  const resultMap = new Map();

  configData.forEach(([country, location]) => {
    if (!country || !location) return;

    // Helper function to convert date to week number
    function getWeekNumber(date) {
      const d = new Date(date);
      d.setHours(0, 0, 0, 0);
      d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
      const week1 = new Date(d.getFullYear(), 0, 4);
      return 1 + Math.round(((d.getTime() - week1.getTime()) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
    }

    // Aggregate data by week for each metric
    const weeklyAggregates = new Map();

    sourceValues.forEach(row => {
      const rowCountry = row[8]; // Column I (0-indexed: 8)
      const rowLocation = row[9]; // Column J (0-indexed: 9)
      const dateValue = row[6];   // Column G (0-indexed: 6)
      const role = row[10];       // Column K (0-indexed: 10)

      if (rowCountry === country && rowLocation === location && (role === "Runner" || role === "RunnerPlus")) {
        const week = getWeekNumber(dateValue);
        const key = `${rowCountry}_${rowLocation}_${week}`;

        if (!weeklyAggregates.has(key)) {
          weeklyAggregates.set(key, {
            country: rowCountry,
            location: rowLocation,
            week: week,
            // Runner metrics
            cumulativeBalance: 0,
            cumulativeNegBalance: 0,
            cumulativePosBalance: 0,
            belowNegThreshold: 0,
            abovePosThreshold: 0,
            peopleBelowNegThreshold: 0,
            peopleAbovePosThreshold: 0,
            // RunnerPlus metrics
            runnerPlusCumulativeBalance: 0,
            runnerPlusCumulativeNegBalance: 0,
            runnerPlusCumulativePosBalance: 0,
            runnerPlusBelowNegThreshold: 0,
            runnerPlusAbovePosThreshold: 0,
            runnerPlusPeopleBelowNegThreshold: 0,
            runnerPlusPeopleAbovePosThreshold: 0
          });
        }

        const aggregate = weeklyAggregates.get(key);
        const balanceValue = row[24] === "" ? 0 : parseFloat(row[24]); // Column Y (0-indexed: 24)
        const peopleCount = row[26] === "" ? 0 : parseFloat(row[26]);  // Column AA (0-indexed: 26)
        const isNegative = row[13]; // Column N (0-indexed: 13)
        const isBelowNegThreshold = row[14]; // Column O (0-indexed: 14)
        const isAbovePosThreshold = row[15]; // Column P (0-indexed: 15)

        if (role === "Runner") {
          // Runner metrics
          aggregate.cumulativeBalance += balanceValue;
          if (isNegative === false) {
            aggregate.cumulativeNegBalance += balanceValue;
          }
          if (isNegative === true) {
            aggregate.cumulativePosBalance += balanceValue;
          }
          if (isBelowNegThreshold === true) {
            aggregate.belowNegThreshold += balanceValue;
            aggregate.peopleBelowNegThreshold += peopleCount;
          }
          if (isAbovePosThreshold === true) {
            aggregate.abovePosThreshold += balanceValue;
            aggregate.peopleAbovePosThreshold += peopleCount;
          }
        } else if (role === "RunnerPlus") {
          // RunnerPlus metrics
          aggregate.runnerPlusCumulativeBalance += balanceValue;
          if (isNegative === false) {
            aggregate.runnerPlusCumulativeNegBalance += balanceValue;
          }
          if (isNegative === true) {
            aggregate.runnerPlusCumulativePosBalance += balanceValue;
          }
          if (isBelowNegThreshold === true) {
            aggregate.runnerPlusBelowNegThreshold += balanceValue;
            aggregate.runnerPlusPeopleBelowNegThreshold += peopleCount;
          }
          if (isAbovePosThreshold === true) {
            aggregate.runnerPlusAbovePosThreshold += balanceValue;
            aggregate.runnerPlusPeopleAbovePosThreshold += peopleCount;
          }
        }
      }
    });

    // Convert aggregates to result format
    for (const [key, aggregate] of weeklyAggregates.entries()) {
      const rowData = [
        aggregate.country,
        aggregate.location,
        aggregate.week,
        null, // Sum deliveries (filled by fetchSlotClosings)
        null, // Weighted slot closing hrs (filled by fetchSlotClosings)
        // Runner Hour Balances (7 columns)
        aggregate.cumulativeBalance,
        aggregate.cumulativeNegBalance,
        aggregate.cumulativePosBalance,
        aggregate.belowNegThreshold,
        aggregate.abovePosThreshold,
        aggregate.peopleBelowNegThreshold,
        aggregate.peopleAbovePosThreshold,
        // RunnerPlus Hour Balances (7 columns)
        aggregate.runnerPlusCumulativeBalance,
        aggregate.runnerPlusCumulativeNegBalance,
        aggregate.runnerPlusCumulativePosBalance,
        aggregate.runnerPlusBelowNegThreshold,
        aggregate.runnerPlusAbovePosThreshold,
        aggregate.runnerPlusPeopleBelowNegThreshold,
        aggregate.runnerPlusPeopleAbovePosThreshold,
        // Retention metrics Runner (3 columns, filled by fetchRetention)
        null, null, null,
        // Avg. Tenure (filled by fetchAvgTenure)
        null,
        // Cancelled deliveries (filled by fetchCancels)
        null, null
      ];

      resultMap.set(key, rowData);
    }
  });

  return resultMap;
}

function fetchRetention() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName("Config");
  const reportingSheet = SpreadsheetApp.openById(OPS_REPORTING);
  const sourceSheet = reportingSheet.getSheetByName("iWD");

  const configData = configSheet.getRange(2, 1, configSheet.getLastRow() - 1, 2).getValues();
  const sourceValues = sourceSheet.getDataRange().getValues();

  const resultMap = new Map();

  configData.forEach(([country, location]) => {
    if (!country || !location) return;

    sourceValues.forEach(row => {
      const rowCountry = row[3];
      const rowLocationRaw = row[9];
      const rowLocation = typeof rowLocationRaw === 'string' ? rowLocationRaw.slice(-3) : String(rowLocationRaw).slice(-3);

      if (rowCountry === country && rowLocation === location) {
        const week = row[6];
        const jobProfile = row[10];
        const emplType = row[11];

        if (emplType === "Total" && jobProfile === "Runner") {
          const ret12Pool = row[26] === "" ? null : parseFloat(row[26]);
          const ret8Pool = row[27] === "" ? null : parseFloat(row[27]);
          const ret8Hire = row[28] === "" ? null : parseFloat(row[28]);

          const key = `${rowCountry}_${location}_${week}`;

          if (!resultMap.has(key)) {
            resultMap.set(key, [
              rowCountry,
              location,
              week,
              null, null, // Sum deliveries, Weighted slot closing hrs
              null, null, null, null, null, null, null, // Hour Balances (7 columns)
              null, null, null, null, null, null, null, null, null, null, null, null, null, null, // Hour Balances RunnerPlus (7 columns)
              null, null, null, // Retention metrics Runner (3 columns)
              null, // Avg. Tenure
              null, null // Cancelled deliveries
            ]);
          }

          const entry = resultMap.get(key);
          entry[19] = ret12Pool; // R 12 week pool retention
          entry[20] = ret8Pool;  // R 8 week pool retention
          entry[21] = ret8Hire;  // R 8 week hire retention
        }
      }
    });
  });

  return resultMap;
}

function fetchAvgTenure(existingWeeksMap) {
  const avgTenureSheet = SpreadsheetApp.openById(AVG_TENURE_ID);
  const sourceSheet = avgTenureSheet.getSheetByName("Avg_tenure");
  const sourceValues = sourceSheet.getDataRange().getValues();

  const resultMap = new Map();
  const currentYear = new Date().getFullYear();

  sourceValues.forEach(row => {
    const country = row[0]; // Column A (0-indexed: 0)
    const location = row[1]; // Column B (0-indexed: 1)
    const week = row[2];     // Column C (0-indexed: 2)
    const year = row[3];     // Column D (0-indexed: 3)
    const avgTenure = row[4] === "" ? null : parseFloat(row[4]); // Column E (0-indexed: 4)

    // Only include data for current year
    if (year === currentYear) {
      const key = `${country}_${location}_${week}`;
      // Store only the tenure value, not a full array
      resultMap.set(key, avgTenure);
    }
  });

  return resultMap;
}

function fetchCancels() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName("Config");
  const reportingSheet = SpreadsheetApp.openById(REPORTING_ID);
  const sourceSheet = reportingSheet.getSheetByName("iDWH");

  const configData = configSheet.getRange(2, 1, configSheet.getLastRow() - 1, 2).getValues();
  const sourceValues = sourceSheet.getDataRange().getValues();

  const aggregationMap = new Map();

  configData.forEach(([country, location]) => {
    if (!country || !location) return;

    sourceValues.forEach(row => {
      const rowCountry = row[3];
      const rowLocation = row[4];

      if (rowCountry === country && rowLocation === location) {
        const week = row[7];
        const rawAE = row[30]; // Column AE (0-indexed: 30)
        const rawDY = row[128]; // Column DY (0-indexed: 128)
        const cancelledDeliveries = rawAE === "" ? 0 : parseFloat(rawAE);
        const distCancelledDeliveries = rawDY === "" ? 0 : parseFloat(rawDY);

        const key = `${rowCountry}_${rowLocation}_${week}`;

        if (!aggregationMap.has(key)) {
          aggregationMap.set(key, {
            country: rowCountry,
            location: rowLocation,
            week,
            cancelledSum: 0,
            distCancelledSum: 0
          });
        }

        const entry = aggregationMap.get(key);
        entry.cancelledSum += cancelledDeliveries;
        entry.distCancelledSum += distCancelledDeliveries;
      }
    });
  });

  const resultMap = new Map();
  for (const [key, entry] of aggregationMap.entries()) {
    const row = [
      entry.country,
      entry.location,
      entry.week,
      null, null, // Sum deliveries, Weighted slot closing hrs (filled by fetchSlotClosings)
      null, null, null, null, null, null, null, // Runner Hour Balances (7 columns, filled by fetchHourBalances)
      null, null, null, null, null, null, null, // RunnerPlus Hour Balances (7 columns, filled by fetchHourBalances)
      null, null, null, // Runner Retention (3 columns, filled by fetchRetention)
      null, // Avg. Tenure (filled by fetchAvgTenure)
      entry.cancelledSum, // Cancelled deliveries
      entry.distCancelledSum // Dist cancelled deliveries
    ];
    resultMap.set(key, row);
  }
  return resultMap;
}
