function importPPSAvailabilities() {
  try {
    // Open availabilities sheet
    const sourceSpreadsheetId = '16PSaiE6-IN6tKcHIqxf1yOmGJ_qFruX4yh-NQxTzimM';
    const sourceSpreadsheet = SpreadsheetApp.openById(sourceSpreadsheetId);
    const sourceSheet = sourceSpreadsheet.getSheetByName('PPS Availabilities');

    if (!sourceSheet) {
      throw new Error('Sheet "PPS Availabilities" not found');
    }

    const makeSchedule = SpreadsheetApp.getActiveSpreadsheet();
    const dashboardSheet = makeSchedule.getSheetByName('Dashboard_L2W');

    if (!dashboardSheet) {
      throw new Error('Sheet "Dashboard_L2W" not found');
    }

    // Search for hub and week
    const hub = dashboardSheet.getRange('B3').getValue().toString();
    const week = parseInt(dashboardSheet.getRange('C2').getValue());

    console.log(`Look for ${hub} week ${week} / ${week-1}`);

    const lastRow = sourceSheet.getLastRow();
    if (lastRow < 2) {
      console.log('No data for hub and week');
      return;
    }

    const sourceData = sourceSheet.getRange(2, 1, lastRow - 1, 15).getValues();
    console.log(`${sourceData.length} availabilities folund`);

    // Daten filtern
    const filteredData = sourceData.filter(row => {
      // Check last 3 characters
      const sourceHub = row[1] ? row[1].toString() : '';
      const sourceHubID = sourceHub.slice(-3);
      const matchesHub = sourceHubID === hub;

      // Check week
      const sourceDate = row[10];
      let matchesWeek = false;

      if (sourceDate instanceof Date) {
        const sourceWeek = getWeekNumber(sourceDate);
        matchesWeek = sourceWeek === week || sourceWeek === (week - 1);
      }

      // Filter unvalid DEFAULTS
      const avSource = row[5] ? row[5].toString().toUpperCase() : '';
      const isValid = row[4] ? row[4].toString().toUpperCase() : '';

      let defaultFilter = true;
      if (avSource === 'DEFAULT') {
        defaultFilter = isValid === 'TRUE';
      }

      // WEEKLY entries are not filtered

      const passesAllFilters = matchesHub && matchesWeek && defaultFilter;

      return passesAllFilters;
    });

    console.log(`${filteredData.length} valid availabilities found.`);

    if (filteredData.length === 0) {
      console.log('No valid availabilities found.');
      return;
    }

    // Push to Make Schedule
    const targetSheet = makeSchedule.getSheetByName('Availabilities');

    if (!targetSheet) {
      throw new Error('Sheet "Availabilities" not found');
    }

    // Delete old data
    const targetLastRow = targetSheet.getLastRow();
    if (targetLastRow > 1) {
      targetSheet.getRange(2, 1, targetLastRow - 1, targetSheet.getLastColumn()).clear();
    }

    // Write new data
    const targetRange = targetSheet.getRange(2, 1, filteredData.length, filteredData[0].length);
    targetRange.setValues(filteredData);

    console.log(`Import done!`);

  } catch (error) {
    console.error('Import error:', error);
  }
}

// Get week number from date
function getWeekNumber(date) {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  const dayNum = d.getUTCDay() || 7;
  d.setUTCDate(d.getUTCDate() + 4 - dayNum);
  const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
}