#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Einfacher Starter für Michaels Screenshots
Verarbeitet Screenshots aus: C:\Users\<USER>\Documents\python\screenshots
"""

import os
import sys

# Unicode-Encoding für Windows-Konsole
if os.name == 'nt':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())

# Versuche das Hauptmodul zu importieren
try:
    from screenshot_kpi_extractor import process_michaels_screenshots, CONFIG
    MAIN_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Hauptmodul nicht gefunden: {e}")
    print("💡 Verwende vereinfachte Version...")
    MAIN_MODULE_AVAILABLE = False

    # Fallback-Konfiguration
    CONFIG = {
        'default_screenshots_path': r'C:\Users\<USER>\Documents\python\screenshots'
    }

    # Einfache OCR-Funktion ohne Tesseract
    def simple_ocr_process():
        """Vereinfachte Verarbeitung ohne Tesseract"""
        import requests
        import os

        screenshots_path = CONFIG['default_screenshots_path']

        if not os.path.exists(screenshots_path):
            print(f"❌ Screenshots-Ordner nicht gefunden: {screenshots_path}")
            return False

        png_files = [f for f in os.listdir(screenshots_path) if f.lower().endswith('.png')]

        if not png_files:
            print(f"❌ Keine PNG-Dateien gefunden")
            return False

        print(f"🔄 Verarbeite {len(png_files)} Screenshots mit OCR.space...")

        results = []

        for png_file in png_files[:2]:  # Nur erste 2 für Demo
            file_path = os.path.join(screenshots_path, png_file)
            print(f"   📸 Verarbeite: {png_file}")

            try:
                with open(file_path, 'rb') as image_file:
                    response = requests.post(
                        'https://api.ocr.space/parse/image',
                        files={'file': image_file},
                        data={
                            'apikey': 'helloworld',
                            'language': 'ger',
                            'isTable': True,
                            'OCREngine': 2
                        },
                        timeout=30
                    )

                result = response.json()
                if result.get('ParsedResults'):
                    ocr_text = result['ParsedResults'][0]['ParsedText']
                    print(f"   ✅ Text erkannt ({len(ocr_text)} Zeichen)")

                    # Einfache Datenextraktion
                    data = extract_simple_data(ocr_text, png_file)
                    if data:
                        results.append(data)
                else:
                    print(f"   ❌ Kein Text erkannt")

            except Exception as e:
                print(f"   ❌ Fehler: {e}")

        if results:
            save_simple_results(results)
            return True
        else:
            print("❌ Keine Daten extrahiert")
            return False

    def extract_simple_data(ocr_text, filename):
        """Einfache Datenextraktion"""
        import re

        lines = [line.strip() for line in ocr_text.split('\n') if line.strip()]

        location = None
        planning_week = None
        expected_delta = None

        for i, line in enumerate(lines):
            line_lower = line.lower()

            # Location und Woche
            if 'week planning' in line_lower:
                week_match = re.search(r'(\d+)', line)
                if week_match:
                    planning_week = int(week_match.group(1))

                if i + 1 < len(lines):
                    location_match = re.search(r'^([A-Z]{2,4})$', lines[i + 1])
                    if location_match:
                        location = location_match.group(1)

            # Expected delta
            if 'expected delta' in line_lower:
                for j in range(i, min(i + 3, len(lines))):
                    delta_match = re.search(r'(-?\d+)', lines[j])
                    if delta_match:
                        expected_delta = int(delta_match.group(1))
                        break

        if location and planning_week:
            return {
                'filename': filename,
                'location': location,
                'planning_week': planning_week,
                'expected_delta': expected_delta
            }
        return None

    def save_simple_results(results):
        """Speichert Ergebnisse in CSV"""
        import csv
        from datetime import datetime

        output_file = 'michael_kpi_simple.csv'

        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['filename', 'location', 'planning_week', 'expected_delta', 'processed_date']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for result in results:
                result['processed_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                writer.writerow(result)

        print(f"✅ Ergebnisse gespeichert in: {output_file}")
        return output_file

def main():
    print("🚀 Michael's Screenshot KPI Extractor")
    print("=" * 50)
    
    screenshots_path = CONFIG['default_screenshots_path']
    print(f"📁 Screenshots-Ordner: {screenshots_path}")
    
    # Prüfe ob Ordner existiert
    if not os.path.exists(screenshots_path):
        print(f"\n❌ Ordner nicht gefunden!")
        print(f"💡 Erstelle den Ordner: {screenshots_path}")
        print(f"📸 Lege deine PNG-Screenshots dort ab")
        
        # Ordner erstellen
        try:
            os.makedirs(screenshots_path, exist_ok=True)
            print(f"✅ Ordner erstellt: {screenshots_path}")
            print(f"📸 Lege jetzt deine Screenshots dort ab und führe das Script erneut aus")
        except Exception as e:
            print(f"❌ Fehler beim Erstellen des Ordners: {e}")
        
        return
    
    # Prüfe ob PNG-Dateien vorhanden
    png_files = [f for f in os.listdir(screenshots_path) if f.lower().endswith('.png')]
    
    if not png_files:
        print(f"\n⚠️  Keine PNG-Dateien gefunden in: {screenshots_path}")
        print(f"📸 Lege deine Screenshots dort ab und führe das Script erneut aus")
        return
    
    print(f"📸 Gefunden: {len(png_files)} PNG-Dateien")
    print(f"📋 Beispiele: {png_files[:3]}")
    
    # Bestätigung
    print(f"\n🔄 Bereit zum Verarbeiten!")

    if MAIN_MODULE_AVAILABLE:
        response = input("Fortfahren mit Hauptmodul? (j/n): ").lower().strip()

        if response in ['j', 'ja', 'y', 'yes', '']:
            print(f"\n🚀 Starte Verarbeitung...")
            success = process_michaels_screenshots()

            if success:
                print(f"\n🎉 Fertig! Öffne 'michael_kpi_data.xlsx' um die Ergebnisse zu sehen")
            else:
                print(f"\n💡 Tipps bei Problemen:")
                print(f"   - Prüfe ob die Screenshots scharf und gut lesbar sind")
                print(f"   - Stelle sicher, dass die KPI-Box sichtbar ist")
                print(f"   - Versuche es mit weniger Screenshots zum Testen")
        else:
            print(f"👋 Abgebrochen")
    else:
        print(f"\n💡 Verwende vereinfachte Version (ohne Tesseract)")
        response = input("Fortfahren mit einfacher OCR? (j/n): ").lower().strip()

        if response in ['j', 'ja', 'y', 'yes', '']:
            print(f"\n🚀 Starte einfache Verarbeitung...")
            success = simple_ocr_process()

            if success:
                print(f"\n🎉 Fertig! Öffne 'michael_kpi_simple.csv' um die Ergebnisse zu sehen")
            else:
                print(f"\n💡 Tipps:")
                print(f"   - Lege Screenshots in: {CONFIG['default_screenshots_path']}")
                print(f"   - Stelle sicher, dass die Bilder scharf sind")
        else:
            print(f"� Abgebrochen")

if __name__ == "__main__":
    main()
