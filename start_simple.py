#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Einfacher Screenshot-Verarbeiter für Michael
<PERSON>oji<PERSON>, nur Text - funktioniert auf allen Windows-Systemen
"""

import os
import sys
import requests
import re
import csv
from datetime import datetime

def main():
    print("=" * 60)
    print("Michael's Screenshot KPI Extractor - Einfache Version")
    print("=" * 60)
    
    # Screenshots-Pfad
    screenshots_path = r'C:\Users\<USER>\Documents\python\screenshots'
    
    print(f"Screenshots-Ordner: {screenshots_path}")
    
    # Prüfe ob Ordner existiert
    if not os.path.exists(screenshots_path):
        print(f"FEHLER: Screenshots-Ordner nicht gefunden!")
        print(f"Erstelle den Ordner und lege PNG-Dateien hinein.")
        
        try:
            os.makedirs(screenshots_path, exist_ok=True)
            print(f"Ordner wurde erstellt: {screenshots_path}")
        except Exception as e:
            print(f"<PERSON><PERSON> be<PERSON>: {e}")
            return
    
    # Suche PNG-<PERSON>ien
    png_files = [f for f in os.listdir(screenshots_path) if f.lower().endswith('.png')]
    
    if not png_files:
        print(f"FEHLER: Keine PNG-Dateien gefunden!")
        print(f"Lege Screenshots in: {screenshots_path}")
        return
    
    print(f"Gefundene PNG-Dateien: {len(png_files)}")
    for i, file in enumerate(png_files[:5], 1):
        print(f"  {i}. {file}")
    
    if len(png_files) > 5:
        print(f"  ... und {len(png_files) - 5} weitere")
    
    # Bestätigung
    print(f"\nBereit zum Verarbeiten!")
    response = input("Fortfahren? (j/n): ").lower().strip()
    
    if response not in ['j', 'ja', 'y', 'yes', '']:
        print("Abgebrochen")
        return
    
    # Anzahl zu verarbeitender Dateien bestimmen
    max_files = min(len(png_files), 10)  # Maximal 10 Dateien

    print(f"\nWie viele Screenshots verarbeiten?")
    print(f"Verfügbar: {len(png_files)} Dateien")
    print(f"Empfohlen: {min(5, len(png_files))} Dateien (für schnellen Test)")

    while True:
        try:
            user_input = input(f"Anzahl (1-{max_files}) oder Enter für alle: ").strip()
            if user_input == "":
                num_files = len(png_files)
                break
            else:
                num_files = int(user_input)
                if 1 <= num_files <= max_files:
                    break
                else:
                    print(f"Bitte Zahl zwischen 1 und {max_files} eingeben")
        except ValueError:
            print("Bitte gültige Zahl eingeben")

    # Verarbeitung starten
    print(f"\nStarte Verarbeitung von {num_files} Screenshots mit OCR.space...")
    results = []

    for i, png_file in enumerate(png_files[:num_files], 1):
        file_path = os.path.join(screenshots_path, png_file)
        print(f"\n[{i}/{num_files}] Verarbeite: {png_file}")
        
        try:
            print(f"  Sende an OCR.space...")

            # OCR.space API aufrufen
            with open(file_path, 'rb') as image_file:
                response = requests.post(
                    'https://api.ocr.space/parse/image',
                    files={'file': image_file},
                    data={
                        'apikey': 'helloworld',
                        'language': 'ger',
                        'isTable': True,
                        'OCREngine': 2
                    },
                    timeout=45  # Längeres Timeout für größere Dateien
                )
            
            result = response.json()
            
            if result.get('ParsedResults'):
                ocr_text = result['ParsedResults'][0]['ParsedText']
                print(f"  OCR erfolgreich ({len(ocr_text)} Zeichen)")
                
                # Daten extrahieren
                data = extract_kpi_data(ocr_text, png_file)
                if data:
                    results.append(data)
                    print(f"  ERFOLG: {data['location']} Week {data['planning_week']} Delta {data['expected_delta']}")
                else:
                    print(f"  Keine vollständigen KPI-Daten gefunden")
            else:
                print(f"  OCR fehlgeschlagen")
                
        except Exception as e:
            print(f"  FEHLER: {e}")
    
    # Zusammenfassung
    print(f"\n" + "="*60)
    print(f"VERARBEITUNG ABGESCHLOSSEN")
    print(f"="*60)
    print(f"Verarbeitete Dateien: {num_files}")
    print(f"Erfolgreiche Extraktionen: {len(results)}")
    print(f"Fehlerhafte Dateien: {num_files - len(results)}")

    # Ergebnisse speichern
    if results:
        output_file = save_results(results)
        print(f"\nERFOLG!")
        print(f"Ergebnisse gespeichert in: {output_file}")

        # Ergebnisse anzeigen
        print(f"\nExtrahierte Daten:")
        print(f"{'Datei':<30} {'Location':<8} {'Week':<6} {'Delta':<8}")
        print(f"-"*60)
        for result in results:
            filename_short = result['filename'][:25] + "..." if len(result['filename']) > 25 else result['filename']
            print(f"{filename_short:<30} {result['location']:<8} {result['planning_week']:<6} {result['expected_delta']:<8}")

        print(f"\nÖffne die CSV-Datei mit:")
        print(f"  notepad {output_file}")
        print(f"  oder importiere in Excel")
    else:
        print(f"\nKeine Daten extrahiert.")
        print(f"Mögliche Ursachen:")
        print(f"  - Screenshots sind unscharf oder zu klein")
        print(f"  - KPI-Box ist nicht sichtbar oder verdeckt")
        print(f"  - Text ist nicht klar lesbar")
        print(f"  - Falsches Dateiformat (nur PNG unterstützt)")
        print(f"\nTipps:")
        print(f"  - Verwende hochauflösende Screenshots")
        print(f"  - Stelle sicher, dass die gesamte KPI-Box sichtbar ist")
        print(f"  - Vermeide Schatten oder Reflexionen")

def extract_kpi_data(ocr_text, filename):
    """Extrahiert KPI-Daten aus OCR-Text"""
    lines = [line.strip() for line in ocr_text.split('\n') if line.strip()]
    
    location = None
    planning_week = None
    expected_delta = None
    
    # Debug: Zeige ersten Teil des Textes
    print(f"  Text-Vorschau: {ocr_text[:100]}...")
    
    for i, line in enumerate(lines):
        line_lower = line.lower()
        
        # Suche nach "Week Planning" und Woche
        if 'week' in line_lower and 'planning' in line_lower:
            week_match = re.search(r'(\d+)', line)
            if week_match:
                planning_week = int(week_match.group(1))
                print(f"    Gefunden: Planning Week {planning_week}")
            
            # Suche Location in nächsten Zeilen
            for j in range(i + 1, min(i + 4, len(lines))):
                location_match = re.search(r'^([A-Z]{2,4})$', lines[j].strip())
                if location_match:
                    location = location_match.group(1)
                    print(f"    Gefunden: Location {location}")
                    break
        
        # Suche nach "Expected Delta"
        if 'expected' in line_lower and 'delta' in line_lower:
            # Suche Zahl in dieser und nächsten Zeilen
            for j in range(i, min(i + 3, len(lines))):
                delta_match = re.search(r'(-?\d+)', lines[j])
                if delta_match:
                    expected_delta = int(delta_match.group(1))
                    print(f"    Gefunden: Expected Delta {expected_delta}")
                    break
    
    # Rückgabe nur wenn mindestens Location und Week gefunden
    if location and planning_week:
        return {
            'filename': filename,
            'location': location,
            'planning_week': planning_week,
            'expected_delta': expected_delta or 0
        }
    
    return None

def save_results(results):
    """Speichert Ergebnisse in CSV-Datei"""
    output_file = 'michael_kpi_results.csv'
    
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['filename', 'location', 'planning_week', 'expected_delta', 'processed_date']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for result in results:
            result['processed_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            writer.writerow(result)
    
    return output_file

if __name__ == "__main__":
    main()
