#!/usr/bin/env python3
"""
Demo Script für den Screenshot KPI Extractor
Erstellt Beispieldaten und zeigt die Verwendung
"""

import pandas as pd
from datetime import datetime
from screenshot_kpi_extractor import ScreenshotKPIExtractor
import os

def create_demo_data():
    """Erstellt Demo-Daten basierend auf deinem Beispiel-Screenshot"""
    demo_data = [
        {
            'filename': 'example_screenshot_week35.png',
            'location': 'AAH',
            'planning_week': 35,
            'expected_delta': -15,
            'shifts_unable_to_assign': 0,
            'trips_below_forecast': 0,
            'runners_submitted_availabilities': 71,
            'processed_date': datetime.now()
        },
        {
            'filename': 'example_screenshot_week36.png',
            'location': 'AAH', 
            'planning_week': 36,
            'expected_delta': 3,
            'shifts_unable_to_assign': 0,
            'trips_below_forecast': None,  # Nicht sichtbar im Screenshot
            'runners_submitted_availabilities': 64,
            'processed_date': datetime.now()
        }
    ]
    
    return demo_data

def demo_excel_output():
    """Demo: Erstellt eine Excel-Datei mit Beispieldaten"""
    print("🔄 Erstelle Demo Excel-Datei...")
    
    data = create_demo_data()
    df = pd.DataFrame(data)
    
    # Excel mit Formatierung erstellen
    output_file = 'demo_kpi_data.xlsx'
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='KPI_Data', index=False)
        
        # Arbeitsblatt formatieren
        worksheet = writer.sheets['KPI_Data']
        
        # Spaltenbreiten anpassen
        column_widths = {
            'A': 25,  # filename
            'B': 10,  # location
            'C': 15,  # planning_week
            'D': 15,  # expected_delta
            'E': 20,  # shifts_unable_to_assign
            'F': 18,  # trips_below_forecast
            'G': 25,  # runners_submitted_availabilities
            'H': 20   # processed_date
        }
        
        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width
        
        # Header formatieren
        for cell in worksheet[1]:
            cell.font = cell.font.copy(bold=True)
            cell.fill = cell.fill.copy(fgColor="4285F4")
    
    print(f"✅ Demo-Datei erstellt: {output_file}")
    return output_file

def demo_with_real_images(image_directory):
    """Demo: Verarbeitet echte Screenshots"""
    if not os.path.exists(image_directory):
        print(f"❌ Verzeichnis nicht gefunden: {image_directory}")
        return
    
    print(f"🔄 Verarbeite Screenshots aus: {image_directory}")
    
    # Extractor konfigurieren
    config = {
        'ocr_method': 'ocr_space',  # Einfacher zu verwenden
        'ocr_space_api_key': 'helloworld',  # Demo API Key
        'output_format': 'excel',
        'output_file': 'real_kpi_data.xlsx',
        'log_level': 'INFO'
    }
    
    extractor = ScreenshotKPIExtractor(config)
    
    try:
        # Screenshots verarbeiten
        data = extractor.process_directory(image_directory)
        
        if data:
            # Daten speichern
            extractor.save_data(data, 'real_kpi_data.xlsx')
            print(f"✅ {len(data)} Screenshots erfolgreich verarbeitet!")
            
            # Kurze Zusammenfassung anzeigen
            df = pd.DataFrame(data)
            print("\n📊 Zusammenfassung:")
            print(f"Locations: {df['location'].unique()}")
            print(f"Wochen: {sorted(df['planning_week'].unique())}")
            print(f"Durchschnittlicher Expected Delta: {df['expected_delta'].mean():.1f}")
            
        else:
            print("❌ Keine Daten extrahiert")
            
    except Exception as e:
        print(f"❌ Fehler: {e}")

def show_usage_examples():
    """Zeigt Verwendungsbeispiele"""
    print("\n" + "="*60)
    print("📖 VERWENDUNGSBEISPIELE")
    print("="*60)
    
    print("\n1️⃣ Kommandozeile - Einfach:")
    print("python screenshot_kpi_extractor.py /pfad/zu/screenshots")
    
    print("\n2️⃣ Kommandozeile - Mit Optionen:")
    print("python screenshot_kpi_extractor.py /pfad/zu/screenshots \\")
    print("    --output meine_daten.xlsx \\")
    print("    --ocr ocr_space \\")
    print("    --verbose")
    
    print("\n3️⃣ Python Code:")
    print("""
from screenshot_kpi_extractor import ScreenshotKPIExtractor

# Extractor erstellen
extractor = ScreenshotKPIExtractor()

# Screenshots verarbeiten
data = extractor.process_directory('/pfad/zu/screenshots')

# Daten speichern
extractor.save_data(data, 'output.xlsx')
""")
    
    print("\n4️⃣ Mit eigener Konfiguration:")
    print("""
config = {
    'ocr_method': 'tesseract',  # oder 'ocr_space'
    'output_format': 'excel',
    'log_level': 'DEBUG'
}

extractor = ScreenshotKPIExtractor(config)
data = extractor.process_directory('/pfad/zu/screenshots')
extractor.save_data(data)
""")

def main():
    print("🚀 Screenshot KPI Extractor - Demo")
    print("="*50)
    
    # Demo-Daten erstellen
    demo_excel_output()
    
    # Verwendungsbeispiele zeigen
    show_usage_examples()
    
    print("\n" + "="*60)
    print("🔧 SETUP-SCHRITTE")
    print("="*60)
    
    print("\n1️⃣ Pakete installieren:")
    print("pip install pytesseract pillow pandas openpyxl requests")
    
    print("\n2️⃣ Tesseract OCR installieren (optional):")
    print("Windows: https://github.com/UB-Mannheim/tesseract/wiki")
    print("Mac: brew install tesseract")
    print("Linux: sudo apt-get install tesseract-ocr")
    
    print("\n3️⃣ Script ausführen:")
    print("python screenshot_kpi_extractor.py /pfad/zu/deinen/screenshots")
    
    print("\n💡 TIPP: Ohne Tesseract wird automatisch OCR.space verwendet!")
    
    # Frage nach echten Screenshots
    print("\n" + "="*60)
    screenshot_dir = input("📁 Pfad zu deinen Screenshots (Enter für Demo): ").strip()
    
    if screenshot_dir and os.path.exists(screenshot_dir):
        demo_with_real_images(screenshot_dir)
    else:
        print("ℹ️  Demo-Modus - Echte Screenshots können später verarbeitet werden")

if __name__ == "__main__":
    main()
