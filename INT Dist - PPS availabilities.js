function requestPPSAvailabilities(){
  var site = "FC6"

  // First week (current week + 2)
  var today = new Date()
  var currentYear = today.getFullYear()
  var currentWeek = Utilities.formatDate(today, "GMT+2", "w")
  var startWeek = parseInt(currentWeek) + 2
  var year = currentYear

  // Handle year overflow if we go beyond week 52/53
  if (startWeek > 52) {
    startWeek = startWeek - 52
    year = currentYear + 1
  }

  Logger.log("Fetching availabilities for 4 weeks starting from week " + year + "/" + startWeek)

  var all_exploded_availabilities = []
  var weeks_to_fetch = []

  // All weeks to fetch for
  for (var i = 0; i < 4; i++) {
    var weekToFetch = startWeek + i
    var yearToFetch = year

    // Handle year overflow
    if (weekToFetch > 52) {
      weekToFetch = weekToFetch - 52
      yearToFetch = year + 1
    }

    weeks_to_fetch.push({year: yearToFetch, week: weekToFetch})

    Logger.log("Fetching data for week " + yearToFetch + "/" + weekToFetch)

    var url = "https://people-availability-proxy-prod.de.picnicinternational.com/availability/final?site_id=" + site + "&year=" + yearToFetch + "&week=" + weekToFetch
    var auth_code = requestAuth()
    var headers = {"Authorization": "Basic " + auth_code}
    var response = UrlFetchApp.fetch(url, {"headers": headers, "method": "GET"})
    var data = JSON.parse(response)

    var exploded_availabilities = explodeDatesAvailabilities(data)
    all_exploded_availabilities = all_exploded_availabilities.concat(exploded_availabilities)
  }

  var target_sheet = SpreadsheetApp.getActive().getSheetByName("DE PPS Availabilities");

  // Overwrite data for current weeks
  if (all_exploded_availabilities.length > 0) {
    overwriteDataForWeeks(target_sheet, weeks_to_fetch, all_exploded_availabilities)
  }
}

function overwriteDataForWeeks(sheet, weeks_to_update, new_data) {
  
  var all_data = sheet.getDataRange().getValues()

  // Create set of year_week to update
  var weeksToUpdateSet = new Set()
  for (var i = 0; i < weeks_to_update.length; i++) {
    weeksToUpdateSet.add(weeks_to_update[i].year + "-W" + String(weeks_to_update[i].week).padStart(2, '0'))
  }

  // Collect rows to keep
  var remaining_data = []

  for (var row = 0; row < all_data.length; row++) {
    if (row === 0) {
      // Keep headers
      remaining_data.push(all_data[row])
    } else {
      var yearWeek = all_data[row][2] // year_week
      if (!weeksToUpdateSet.has(yearWeek)) {
        // Keep older rows
        remaining_data.push(all_data[row])
      }
      
    }
  }

  // Add new data
  for (var i = 0; i < new_data.length; i++) {
    remaining_data.push(new_data[i])
  }

  // Push all data back to sheet
  sheet.clear()
  if (remaining_data.length > 0) {
    sheet.getRange(1, 1, remaining_data.length, remaining_data[0].length).setValues(remaining_data)
  }

  Logger.log("Overwrote data for weeks: " + Array.from(weeksToUpdateSet).join(", ") + " with " + new_data.length + " new rows")
}

function explodeDatesAvailabilities(data) {
  var availabilities_exploded = []

  // Go through all PNs
  for (i = 0; i < data.length; i++) {
    var worker_record = data[i]
    var worker_id = worker_record["worker_id"]
    var site = worker_record["site"]
    var year_week = worker_record["year_week"]
    var requested_shifts = worker_record["requested_shifts"]
    var valid = worker_record["valid"]
    var source = worker_record["source"]
    var submitted_at = worker_record["submitted_at"]
    var created_at = worker_record["created_at"]

    // Go through all availabilities per PN
    var availabilities = worker_record["availabilities"]
    if (availabilities && availabilities.length > 0) {
      for (j = 0; j < availabilities.length; j++) {
        var availability = availabilities[j]
        var availability_id = availability["id"]
        var template_id = availability["template_id"]
        var start_time = availability["start_time"]
        var shift_name = availability["shift_name"]
        var duration = availability["duration"]
        var availability_valid = availability["valid"]

        // Convert start_time to readable date and time
        var formatted_date = ""
        var formatted_time = ""
        try {
          var start_date = new Date(start_time)
          formatted_date = Utilities.formatDate(start_date, "GMT+2", "dd/MM/yyyy")
          formatted_time = Utilities.formatDate(start_date, "GMT+2", "HH:mm")
        } catch(e) {
          formatted_date = start_time
          formatted_time = ""
        }

        availabilities_exploded.push([
          worker_id,
          site,
          year_week,
          requested_shifts,
          valid,
          source,
          submitted_at,
          created_at,
          availability_id,
          template_id,
          formatted_date,
          formatted_time,
          shift_name,
          duration,
          availability_valid
        ])
      }
    }
  }
  return availabilities_exploded
}