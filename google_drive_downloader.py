#!/usr/bin/env python3
"""
Google Drive Screenshot Downloader
Lädt Screenshots aus Google Drive herunter und verarbeitet sie

Benötigt:
pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib

Setup:
1. Google Cloud Console: https://console.cloud.google.com/
2. APIs & Services → Credentials
3. Create Credentials → OAuth 2.0 Client ID
4. Download JSO<PERSON> als 'credentials.json'
"""

import os
import io
from pathlib import Path
import logging

try:
    from googleapiclient.discovery import build
    from google_auth_oauthlib.flow import InstalledAppFlow
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from googleapiclient.http import MediaIoBaseDownload
    GOOGLE_API_AVAILABLE = True
except ImportError:
    GOOGLE_API_AVAILABLE = False
    print("Google API nicht verfügbar. Installiere mit:")
    print("pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib")

from screenshot_kpi_extractor import ScreenshotKPIExtractor

# Google Drive API Scopes
SCOPES = ['https://www.googleapis.com/auth/drive.readonly']

logger = logging.getLogger(__name__)

class GoogleDriveScreenshotProcessor:
    def __init__(self, credentials_file='credentials.json', token_file='token.json'):
        self.credentials_file = credentials_file
        self.token_file = token_file
        self.service = None
        
    def authenticate(self):
        """Google Drive API Authentifizierung"""
        if not GOOGLE_API_AVAILABLE:
            raise Exception("Google API Pakete nicht installiert")
        
        creds = None
        
        # Token laden falls vorhanden
        if os.path.exists(self.token_file):
            creds = Credentials.from_authorized_user_file(self.token_file, SCOPES)
        
        # Falls keine gültigen Credentials, neue Authentifizierung
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                if not os.path.exists(self.credentials_file):
                    raise FileNotFoundError(
                        f"Credentials-Datei nicht gefunden: {self.credentials_file}\n"
                        "Lade sie von Google Cloud Console herunter"
                    )
                
                flow = InstalledAppFlow.from_client_secrets_file(
                    self.credentials_file, SCOPES)
                creds = flow.run_local_server(port=0)
            
            # Token speichern
            with open(self.token_file, 'w') as token:
                token.write(creds.to_json())
        
        self.service = build('drive', 'v3', credentials=creds)
        logger.info("Google Drive API authentifiziert")
    
    def list_files_in_folder(self, folder_id, file_type='png'):
        """Listet alle Dateien eines bestimmten Typs in einem Google Drive Ordner"""
        if not self.service:
            self.authenticate()
        
        query = f"'{folder_id}' in parents and name contains '.{file_type}' and trashed=false"
        
        results = self.service.files().list(
            q=query,
            fields="nextPageToken, files(id, name, size, modifiedTime)"
        ).execute()
        
        files = results.get('files', [])
        logger.info(f"Gefunden: {len(files)} {file_type.upper()}-Dateien in Ordner")
        
        return files
    
    def download_file(self, file_id, file_name, download_dir):
        """Lädt eine Datei von Google Drive herunter"""
        if not self.service:
            self.authenticate()
        
        # Download-Verzeichnis erstellen
        download_path = Path(download_dir)
        download_path.mkdir(parents=True, exist_ok=True)
        
        # Datei herunterladen
        request = self.service.files().get_media(fileId=file_id)
        file_path = download_path / file_name
        
        with open(file_path, 'wb') as fh:
            downloader = MediaIoBaseDownload(fh, request)
            done = False
            while done is False:
                status, done = downloader.next_chunk()
        
        logger.info(f"Heruntergeladen: {file_name}")
        return file_path
    
    def download_screenshots_from_folder(self, folder_id, download_dir='downloaded_screenshots'):
        """Lädt alle Screenshots aus einem Google Drive Ordner herunter"""
        logger.info(f"Lade Screenshots aus Google Drive Ordner: {folder_id}")
        
        # Dateien auflisten
        files = self.list_files_in_folder(folder_id, 'png')
        
        if not files:
            logger.warning("Keine PNG-Dateien gefunden")
            return []
        
        # Dateien herunterladen
        downloaded_files = []
        for file_info in files:
            try:
                file_path = self.download_file(
                    file_info['id'], 
                    file_info['name'], 
                    download_dir
                )
                downloaded_files.append(file_path)
            except Exception as e:
                logger.error(f"Fehler beim Download von {file_info['name']}: {e}")
        
        logger.info(f"Erfolgreich {len(downloaded_files)} Dateien heruntergeladen")
        return downloaded_files
    
    def process_screenshots_from_drive(self, folder_id, output_file='kpi_data_from_drive.xlsx'):
        """Kompletter Workflow: Download + OCR + Excel"""
        logger.info("Starte kompletten Workflow...")
        
        # 1. Screenshots herunterladen
        download_dir = 'temp_screenshots'
        downloaded_files = self.download_screenshots_from_folder(folder_id, download_dir)
        
        if not downloaded_files:
            logger.error("Keine Dateien heruntergeladen")
            return None
        
        # 2. OCR verarbeiten
        extractor = ScreenshotKPIExtractor({
            'ocr_method': 'ocr_space',  # Einfacher für Demo
            'log_level': 'INFO'
        })
        
        data = extractor.process_directory(download_dir)
        
        # 3. Daten speichern
        if data:
            extractor.save_data(data, output_file)
            logger.info(f"Daten gespeichert in: {output_file}")
        
        # 4. Temp-Dateien löschen (optional)
        # shutil.rmtree(download_dir)
        
        return data

def setup_google_drive_api():
    """Hilft beim Setup der Google Drive API"""
    print("🔧 Google Drive API Setup")
    print("=" * 50)
    
    print("\n1️⃣ Google Cloud Console öffnen:")
    print("   https://console.cloud.google.com/")
    
    print("\n2️⃣ Neues Projekt erstellen oder auswählen")
    
    print("\n3️⃣ Google Drive API aktivieren:")
    print("   APIs & Services → Library → Google Drive API → Enable")
    
    print("\n4️⃣ Credentials erstellen:")
    print("   APIs & Services → Credentials → Create Credentials")
    print("   → OAuth 2.0 Client ID → Desktop Application")
    
    print("\n5️⃣ JSON herunterladen:")
    print("   Download JSON und als 'credentials.json' speichern")
    
    print("\n6️⃣ Pakete installieren:")
    print("   pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib")
    
    print("\n7️⃣ Script ausführen:")
    print("   python google_drive_downloader.py")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Google Drive Screenshot Processor')
    parser.add_argument('folder_id', nargs='?', help='Google Drive Ordner ID')
    parser.add_argument('-o', '--output', default='kpi_data_from_drive.xlsx', help='Ausgabedatei')
    parser.add_argument('--setup', action='store_true', help='Setup-Anleitung anzeigen')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.basicConfig(level=logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO)
    
    if args.setup:
        setup_google_drive_api()
        return
    
    if not args.folder_id:
        print("❌ Google Drive Ordner ID fehlt")
        print("\n💡 Ordner ID finden:")
        print("   1. Öffne den Ordner in Google Drive")
        print("   2. Kopiere die ID aus der URL:")
        print("   https://drive.google.com/drive/folders/DIESE_ID_HIER")
        print("\n📖 Hilfe: python google_drive_downloader.py --setup")
        return 1
    
    try:
        processor = GoogleDriveScreenshotProcessor()
        data = processor.process_screenshots_from_drive(args.folder_id, args.output)
        
        if data:
            print(f"\n✅ Erfolgreich {len(data)} Screenshots verarbeitet!")
            print(f"💾 Ausgabedatei: {args.output}")
        else:
            print("❌ Keine Daten extrahiert")
            return 1
            
    except Exception as e:
        logger.error(f"Fehler: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
