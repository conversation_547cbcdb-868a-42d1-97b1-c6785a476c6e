// Scheduling logs
const SOURCE_SHEETS = {
  DE_LOG: '1Kps_3Ryb4zE3Ekowq7O1yu7IcUGUoLqpV-JoxQEMcFw',
  NL_LOG: '1QXGZBYot4p5Bpwoylxttf8qCQ9nnwh8_oj82moEruxw',
  FR_LOG: '15V-_BTPD6lZqKTbr56nOOrVy0UTlMOnAWr6B5I6xZdw'
};

// Availability sheets for hubs
const HUB_SHEETS = {
  DE_HUBS: '18s1fDiX9p0nCe_0oBwjaD2JoR98BlI23C5RTreFTGHM',
  NL_HUBS: '1cjpyTyaec7QCiaZlBpE8Rbv0T4gs9eyU_7xXyu-vBN0',
  FR_HUBS: '1Bdb455bopOt7dcFsqlfiD9xPW1XZaPnIvc306e7uRLo'
};

// Ranges
const COUNTRY_CONFIG = {
  DE: {
    hubCell: 'AQ3',
    weekCell: 'AR2',
    yearCell: 'AR3',
    shiftsRequiredRange: 'AT21:AZ35',
    shiftsPlannedRange: 'AT37:AZ51',
    negHrsWithoutNewHires: 'BD34',
    negHrs: 'BD31',
    posHrs: 'BD27'
  },
  NL: {
    hubCell: 'B3',
    weekCell: 'C2',
    yearCell: 'C3',
    shiftsRequiredRange: 'E21:K35',
    shiftsPlannedRange: 'E37:K51',
    negHrsWithoutNewHires: 'O34',
    negHrs: 'O31',
    posHrs: 'O27'
  },
  FR: {
    hubCell: 'B3',
    weekCell: 'C2',
    yearCell: 'C3',
    shiftsRequiredRange: 'E21:K35',
    shiftsPlannedRange: 'E37:K51',
    negHrsWithoutNewHires: 'O34',
    negHrs: 'O31',
    posHrs: 'O27'
  }
};

/**
 * 
 */
function collectSchedulingResults() {
  try {
    const historySheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('INT Scheduling Results');
    if (!historySheet) {
      throw new Error('"INT Scheduling Results" not found');
    }

    setHeaders(historySheet);

    const allData = [];

    for (const country of ['DE', 'NL', 'FR']) {
      console.log(`Fetch data for ${country}...`);
      const countryData = collectCountryData(country);
      allData.push(...countryData);
    }

    // Push data
    if (allData.length > 0) {
      // Delete data
      const lastRow = historySheet.getLastRow();
      if (lastRow > 1) {
        historySheet.deleteRows(2, lastRow - 1);
      }

      // Push new data
      const range = historySheet.getRange(2, 1, allData.length, allData[0].length);
      range.setValues(allData);

      console.log(`${allData.length} loaded`);
    } else {
      console.log('No data found');
    }

  } catch (error) {
    console.error('Fehler beim Sammeln der Daten:', error);
    throw error;
  }
}

/**
 * Header für das Ziel-Sheet setzen
 */
function setHeaders(sheet) {
  const headers = [
    'Country',
    'Hub',
    'Week',
    'Year',
    'Shifts Required',
    'Shifts Planned',
    '', // Array formula
    'Neg. hrs. delta without new hires',
    'Neg. hrs. delta',
    'Pos. hrs. delta'
  ];

  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
}

/**
 * Daten für ein bestimmtes Land sammeln
 */
function collectCountryData(country) {
  const hubNames = getHubNames(country);
  const logSheetId = SOURCE_SHEETS[`${country}_LOG`];
  const config = COUNTRY_CONFIG[country];
  const data = [];

  for (const hubName of hubNames) {
    try {
      const hubData = getHubData(logSheetId, hubName, country, config);
      if (hubData) {
        data.push(hubData);
      }
    } catch (error) {
      console.warn(`Fehler beim Laden der Daten für Hub ${hubName} in ${country}:`, error);
    }
  }

  return data;
}

/**
 * Hub-Namen aus dem entsprechenden Hub-Sheet ermitteln
 */
function getHubNames(country) {
  try {
    const hubSheetId = HUB_SHEETS[`${country}_HUBS`];
    const hubSheet = SpreadsheetApp.openById(hubSheetId);
    const sheets = hubSheet.getSheets();

    // Alle 3-Zeichen Sheet-Namen sammeln
    const hubNames = [];
    for (const sheet of sheets) {
      const sheetName = sheet.getName();
      if (sheetName.length === 3) {
        hubNames.push(sheetName);
      }
    }

    console.log(`Gefundene Hubs für ${country}:`, hubNames);
    return hubNames;

  } catch (error) {
    console.error(`Fehler beim Ermitteln der Hub-Namen für ${country}:`, error);
    return [];
  }
}

/**
 * Daten für einen spezifischen Hub laden
 */
function getHubData(logSheetId, hubName, country, config) {
  try {
    const logSpreadsheet = SpreadsheetApp.openById(logSheetId);
    const hubSheet = logSpreadsheet.getSheetByName(hubName);

    if (!hubSheet) {
      console.warn(`Sheet ${hubName} nicht gefunden in ${country}_LOG`);
      return null;
    }

    // Einzelne Werte lesen
    const hub = hubSheet.getRange(config.hubCell).getValue();
    const week = hubSheet.getRange(config.weekCell).getValue();
    const year = hubSheet.getRange(config.yearCell).getValue();
    const negHrsWithoutNewHires = hubSheet.getRange(config.negHrsWithoutNewHires).getValue();
    const negHrs = hubSheet.getRange(config.negHrs).getValue();
    const posHrs = hubSheet.getRange(config.posHrs).getValue();

    // Summen berechnen
    const shiftsRequired = calculateSum(hubSheet, config.shiftsRequiredRange);
    const shiftsPlanned = calculateSum(hubSheet, config.shiftsPlannedRange);

    return [
      country,
      hub,
      week,
      year,
      shiftsRequired,
      shiftsPlanned,
      '', // Spalte G bleibt frei
      negHrsWithoutNewHires,
      negHrs,
      posHrs
    ];

  } catch (error) {
    console.error(`Fehler beim Laden der Hub-Daten für ${hubName}:`, error);
    return null;
  }
}

/**
 * Summe eines Bereichs berechnen
 */
function calculateSum(sheet, range) {
  try {
    const values = sheet.getRange(range).getValues();
    let sum = 0;

    for (const row of values) {
      for (const cell of row) {
        if (typeof cell === 'number' && !isNaN(cell)) {
          sum += cell;
        }
      }
    }

    return sum;
  } catch (error) {
    console.error(`Fehler beim Berechnen der Summe für Bereich ${range}:`, error);
    return 0;
  }
}

/**
 * Hilfsfunktionen zum Testen einzelner Länder
 */
function testDE() {
  const data = collectCountryData('DE');
  console.log('DE Daten:', data);
}

function testNL() {
  const data = collectCountryData('NL');
  console.log('NL Daten:', data);
}

function testFR() {
  const data = collectCountryData('FR');
  console.log('FR Daten:', data);
}