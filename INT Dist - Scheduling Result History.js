// Scheduling logs
const SOURCE_SHEETS = {
  DE_LOG: '1Kps_3Ryb4zE3Ekowq7O1yu7IcUGUoLqpV-JoxQEMcFw',
  NL_LOG: '1QXGZBYot4p5Bpwoylxttf8qCQ9nnwh8_oj82moEruxw',
  FR_LOG: '15V-_BTPD6lZqKTbr56nOOrVy0UTlMOnAWr6B5I6xZdw'
};

// Availability sheets for hubs
const HUB_SHEETS = {
  DE_HUBS: '18s1fDiX9p0nCe_0oBwjaD2JoR98BlI23C5RTreFTGHM',
  NL_HUBS: '1cjpyTyaec7QCiaZlBpE8Rbv0T4gs9eyU_7xXyu-vBN0',
  FR_HUBS: '1Bdb455bopOt7dcFsqlfiD9xPW1XZaPnIvc306e7uRLo'
};

// Ranges
const COUNTRY_CONFIG = {
  DE: {
    hubCell: 'AQ3',
    weekCell: 'AR2',
    yearCell: 'AR3',
    shiftsRequiredRange: 'AT21:AZ35',
    shiftsPlannedRange: 'AT37:AZ51',
    negHrsWithoutNewHires: 'BD34',
    negHrs: 'BD31',
    posHrs: 'BD27'
  },
  NL: {
    hubCell: 'B3',
    weekCell: 'C2',
    yearCell: 'C3',
    shiftsRequiredRange: 'E21:K35',
    shiftsPlannedRange: 'E37:K51',
    negHrsWithoutNewHires: 'O34',
    negHrs: 'O31',
    posHrs: 'O27'
  },
  FR: {
    hubCell: 'B3',
    weekCell: 'C2',
    yearCell: 'C3',
    shiftsRequiredRange: 'E21:K35',
    shiftsPlannedRange: 'E37:K51',
    negHrsWithoutNewHires: 'O34',
    negHrs: 'O31',
    posHrs: 'O27'
  }
};

/**
 * Main function
 */
function fetchSchedulingResults() {
  try {
    const historySheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('INT Scheduling Results');
    if (!historySheet) {
      throw new Error('"INT Scheduling Results" not found');
    }

    setHeaders(historySheet);

    const allData = [];

    for (const country of ['DE', 'NL', 'FR']) {
      console.log(`Fetch data for ${country}...`);
      const countryData = collectCountryData(country);
      allData.push(...countryData);
    }

    // Push data while preserving older weeks
    if (allData.length > 0) {
      updateDataPreservingOlderWeeks(historySheet, allData);
      console.log(`${allData.length} records processed`);
    } else {
      console.log('No data found');
    }

  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
}

/**
 * Set headers
 */
function setHeaders(sheet) {
  const headers = [
    'Country',
    'Hub',
    'Week',
    'Year',
    'Shifts Required',
    'Shifts Planned',
    '', // Array formula
    'Neg. hrs. delta without new hires',
    'Neg. hrs. delta',
    'Pos. hrs. delta'
  ];

  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
}

/**
 * Update data while preserving older weeks
 */
function updateDataPreservingOlderWeeks(sheet, newData) {
  // Get current weeks from new data by country
  const currentWeeksByCountry = getCurrentWeeksByCountry(newData);

  // Get existing data
  const lastRow = sheet.getLastRow();
  if (lastRow <= 1) {
    // No existing data, just insert new data
    const range = sheet.getRange(2, 1, newData.length, newData[0].length);
    range.setValues(newData);
    return;
  }

  const existingData = sheet.getRange(2, 1, lastRow - 1, 10).getValues();

  // Filter existing data to keep only older weeks
  const dataToKeep = [];
  for (const row of existingData) {
    const country = row[0]; // Column A
    const year = row[3];    // Column D
    const week = row[2];    // Column C

    if (country && currentWeeksByCountry[country]) {
      const currentWeek = currentWeeksByCountry[country].week;
      const currentYear = currentWeeksByCountry[country].year;

      // Keep data if it's from an earlier year or earlier week in same year
      if (year < currentYear || (year === currentYear && week < currentWeek)) {
        dataToKeep.push(row);
      }
    } else {
      // Keep data for countries not in new data
      dataToKeep.push(row);
    }
  }

  // Combine old data with new data
  const allData = [...dataToKeep, ...newData];

  // Clear existing data and write combined data
  if (lastRow > 1) {
    sheet.deleteRows(2, lastRow - 1);
  }

  if (allData.length > 0) {
    const range = sheet.getRange(2, 1, allData.length, allData[0].length);
    range.setValues(allData);
  }

  console.log(`Kept ${dataToKeep.length} older records, added ${newData.length} new records`);
}

/**
 * Get current weeks from new data by country
 */
function getCurrentWeeksByCountry(newData) {
  const weeksByCountry = {};

  for (const row of newData) {
    const country = row[0]; // Column A
    const week = row[2];    // Column C
    const year = row[3];    // Column D

    if (!weeksByCountry[country] ||
        year > weeksByCountry[country].year ||
        (year === weeksByCountry[country].year && week > weeksByCountry[country].week)) {
      weeksByCountry[country] = { week: week, year: year };
    }
  }

  return weeksByCountry;
}

/**
 * Fetch data for country
 */
function collectCountryData(country) {
  const hubNames = getHubNames(country);
  const logSheetId = SOURCE_SHEETS[`${country}_LOG`];
  const config = COUNTRY_CONFIG[country];
  const data = [];

  for (const hubName of hubNames) {
    try {
      const hubData = getHubData(logSheetId, hubName, country, config);
      if (hubData) {
        data.push(hubData);
      }
    } catch (error) {
      console.warn(`Error fetching data for ${country}-${hubName}:`, error);
    }
  }

  return data;
}

/**
 * Get hub names from avs. sheet
 */
function getHubNames(country) {
  try {
    const hubSheetId = HUB_SHEETS[`${country}_HUBS`];
    const hubSheet = SpreadsheetApp.openById(hubSheetId);
    const sheets = hubSheet.getSheets();

    const hubNames = [];
    for (const sheet of sheets) {
      const sheetName = sheet.getName();
      if (sheetName.length === 3) {
        hubNames.push(sheetName);
      }
    }

    console.log(`Found hubs for ${country}:`, hubNames);
    return hubNames;

  } catch (error) {
    console.error(`Error for ${country}:`, error);
    return [];
  }
}

/**
 * Get hub data from log sheet
 */
function getHubData(logSheetId, hubName, country, config) {
  try {
    const logSpreadsheet = SpreadsheetApp.openById(logSheetId);
    const hubSheet = logSpreadsheet.getSheetByName(hubName);

    if (!hubSheet) {
      console.warn(`Sheet ${hubName} not found in ${country}_LOG`);
      return null;
    }

    const hub = hubSheet.getRange(config.hubCell).getValue();
    const week = hubSheet.getRange(config.weekCell).getValue();
    const year = hubSheet.getRange(config.yearCell).getValue();
    const negHrsWithoutNewHires = hubSheet.getRange(config.negHrsWithoutNewHires).getValue();
    const negHrs = hubSheet.getRange(config.negHrs).getValue();
    const posHrs = hubSheet.getRange(config.posHrs).getValue();

    const shiftsRequired = calculateSum(hubSheet, config.shiftsRequiredRange);
    const shiftsPlanned = calculateSum(hubSheet, config.shiftsPlannedRange);

    return [
      country,
      hub,
      week,
      year,
      shiftsRequired,
      shiftsPlanned,
      '', // Array formula
      negHrsWithoutNewHires,
      negHrs,
      posHrs
    ];

  } catch (error) {
    console.error(`Error for ${hubName}:`, error);
    return null;
  }
}

/**
 * Get sum
 */
function calculateSum(sheet, range) {
  try {
    const values = sheet.getRange(range).getValues();
    let sum = 0;

    for (const row of values) {
      for (const cell of row) {
        if (typeof cell === 'number' && !isNaN(cell)) {
          sum += cell;
        }
      }
    }

    return sum;
  } catch (error) {
    console.error(`Error for ${range}:`, error);
    return 0;
  }
}

/**
 * Test functions
 */
function testDE() {
  const data = collectCountryData('DE');
  console.log('DE data:', data);
}

function testNL() {
  const data = collectCountryData('NL');
  console.log('NL data:', data);
}

function testFR() {
  const data = collectCountryData('FR');
  console.log('FR data:', data);
}