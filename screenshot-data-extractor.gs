/**
 * Google Apps Script für das Extrahieren von KPI-Daten aus Schichtplanungs-Screenshots
 * Verwendet Google Cloud Vision API für OCR
 *
 * Extrahiert folgende Daten:
 * - Location (3-Zeichen Kürzel)
 * - Planungswoche
 * - Expected delta in neg. hour balances
 * - Shifts unable to be assigned
 * - Trips below forecast
 * - Runners submitted availabilities
 */

// Konfiguration - Diese Werte musst du anpassen
const CONFIG = {
  // Google Drive Ordner ID mit den Screenshots
  DRIVE_FOLDER_ID: '1DMuxRmYdZE0yJGV1G42e8bobLImwF0zL',

  // Google Sheet ID wo die Daten gespeichert werden sollen (wird automatisch erstellt wenn leer)
  TARGET_SHEET_ID: '1i5gLZZ5v83ImFCuPL-EXg2MLi90xx7RIolcFIawXb-Y',

  // Name des Arbeitsblatts
  SHEET_NAME: 'DE',

  // Google Cloud Vision API Key (optional, falls nicht über GCP Projekt verfügbar)
  VISION_API_KEY: '', // <PERSON><PERSON> lassen wenn GCP Projekt verwendet wird

  // Automatisch neues Sheet erstellen falls TARGET_SHEET_ID leer ist
  AUTO_CREATE_SHEET: true,
  NEW_SHEET_NAME: 'KPI Dashboard Daten'
};

/**
 * Hauptfunktion - Verarbeitet alle Screenshots im angegebenen Ordner
 */
function processAllScreenshots() {
  try {
    console.log('Starte Verarbeitung der Screenshots...');

    // Zugriff auf den Drive Ordner
    const folder = DriveApp.getFolderById(CONFIG.DRIVE_FOLDER_ID);
    const files = folder.getFilesByType(MimeType.PNG);

    // Ziel-Sheet vorbereiten oder erstellen
    const targetSheet = prepareOrCreateTargetSheet();

    let processedCount = 0;
    const allExtractedData = [];

    // Alle PNG-Dateien verarbeiten
    while (files.hasNext()) {
      const file = files.next();
      console.log(`Verarbeite: ${file.getName()}`);

      try {
        const extractedData = processScreenshot(file);
        if (extractedData && extractedData.length > 0) {
          allExtractedData.push(...extractedData);
          processedCount++;
        }
      } catch (error) {
        console.error(`Fehler bei ${file.getName()}: ${error.message}`);
      }
    }

    // Daten in Sheet schreiben
    if (allExtractedData.length > 0) {
      writeDataToSheet(targetSheet, allExtractedData);
      console.log(`Erfolgreich ${processedCount} Screenshots verarbeitet und ${allExtractedData.length} Datensätze extrahiert.`);
      console.log(`Sheet URL: ${targetSheet.getParent().getUrl()}`);
    } else {
      console.log('Keine Daten extrahiert.');
    }

  } catch (error) {
    console.error(`Hauptfehler: ${error.message}`);
    throw error;
  }
}

/**
 * Demo-Funktion: Erstellt Beispieldaten aus deinem Screenshot
 */
function createDemoDataFromExample() {
  try {
    console.log('Erstelle Demo-Daten aus Beispiel-Screenshot...');

    // Ziel-Sheet vorbereiten oder erstellen
    const targetSheet = prepareOrCreateTargetSheet();

    // Beispieldaten aus deinem Screenshot
    const demoData = [
      {
        fileName: 'example_screenshot_week35.png',
        location: 'AAH',
        planningWeek: 35,
        expectedDelta: -15,
        shiftsUnableToAssign: 0,
        tripsBelowForecast: 0,
        runnersSubmittedAvailabilities: 71,
        processedDate: new Date()
      },
      {
        fileName: 'example_screenshot_week36.png',
        location: 'AAH',
        planningWeek: 36,
        expectedDelta: 3,
        shiftsUnableToAssign: 0,
        tripsBelowForecast: null, // Nicht sichtbar im Screenshot
        runnersSubmittedAvailabilities: 64,
        processedDate: new Date()
      }
    ];

    // Demo-Daten in Sheet schreiben
    writeDataToSheet(targetSheet, demoData);

    console.log(`Demo-Daten erfolgreich erstellt!`);
    console.log(`Sheet URL: ${targetSheet.getParent().getUrl()}`);

    return targetSheet.getParent().getUrl();

  } catch (error) {
    console.error(`Demo-Fehler: ${error.message}`);
    throw error;
  }
}

/**
 * Verarbeitet einen einzelnen Screenshot
 */
function processScreenshot(file) {
  const imageBlob = file.getBlob();
  const ocrText = performOCR(imageBlob);
  
  if (!ocrText) {
    console.log(`Kein Text in ${file.getName()} erkannt`);
    return [];
  }
  
  return parseScheduleData(ocrText, file.getName());
}

/**
 * Führt OCR mit OCR.space API durch (Alternative zu Google Vision)
 */
function performOCR(imageBlob) {
  try {
    // Option 1: OCR.space API (kostenlos, einfach)
    return performOCRWithOCRSpace(imageBlob);

    // Option 2: Google Vision API (auskommentiert)
    // return performOCRWithGoogleVision(imageBlob);

  } catch (error) {
    console.error(`OCR Fehler: ${error.message}`);
    return null;
  }
}

/**
 * OCR mit OCR.space API (kostenlos, 25.000 Anfragen/Monat)
 */
function performOCRWithOCRSpace(imageBlob) {
  const apiKey = 'helloworld'; // Kostenloser API Key für Tests
  // Für Produktion: Registriere dich auf ocr.space für eigenen API Key

  const formData = {
    'apikey': apiKey,
    'language': 'ger', // Deutsch
    'isOverlayRequired': false,
    'detectOrientation': true,
    'isTable': true, // Optimiert für Tabellen
    'OCREngine': 2 // Engine 2 ist besser für Tabellen
  };

  const response = UrlFetchApp.fetch('https://api.ocr.space/parse/image', {
    method: 'POST',
    payload: {
      ...formData,
      'file': imageBlob
    }
  });

  const result = JSON.parse(response.getContentText());

  if (result.ParsedResults && result.ParsedResults.length > 0) {
    return result.ParsedResults[0].ParsedText;
  }

  return null;
}

/**
 * OCR mit Google Cloud Vision API (Original-Implementierung)
 */
function performOCRWithGoogleVision(imageBlob) {
  const base64Image = Utilities.base64Encode(imageBlob.getBytes());

  const requestBody = {
    requests: [{
      image: {
        content: base64Image
      },
      features: [{
        type: 'TEXT_DETECTION',
        maxResults: 1
      }]
    }]
  };

  let response;
  if (CONFIG.VISION_API_KEY) {
    // Mit API Key
    const url = `https://vision.googleapis.com/v1/images:annotate?key=${CONFIG.VISION_API_KEY}`;
    response = UrlFetchApp.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(requestBody)
    });
  } else {
    // Mit OAuth (GCP Projekt)
    const url = 'https://vision.googleapis.com/v1/images:annotate';
    response = UrlFetchApp.fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${ScriptApp.getOAuthToken()}`,
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(requestBody)
    });
  }

  const result = JSON.parse(response.getContentText());

  if (result.responses && result.responses[0] && result.responses[0].textAnnotations) {
    return result.responses[0].textAnnotations[0].description;
  }

  return null;
}

/**
 * Parst die OCR-Textdaten und extrahiert KPI-Informationen
 */
function parseScheduleData(ocrText, fileName) {
  const lines = ocrText.split('\n').map(line => line.trim()).filter(line => line.length > 0);

  let location = null;
  let planningWeek = null;
  let expectedDelta = null;
  let shiftsUnableToAssign = null;
  let tripsBelowForecast = null;
  let runnersSubmittedAvailabilities = null;

  console.log(`Verarbeite ${fileName}:`);
  console.log('OCR Text:', ocrText.substring(0, 500) + '...');

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const nextLine = i + 1 < lines.length ? lines[i + 1] : '';

    // Location und Planungswoche aus "Week Planning" Zeile
    const weekPlanningMatch = line.match(/Week\s+Planning\s*(\d+)?/i);
    if (weekPlanningMatch) {
      // Planungswoche direkt in der Zeile oder in der nächsten Zeile
      if (weekPlanningMatch[1]) {
        planningWeek = parseInt(weekPlanningMatch[1]);
      } else {
        const weekNumberMatch = nextLine.match(/^(\d+)$/);
        if (weekNumberMatch) {
          planningWeek = parseInt(weekNumberMatch[1]);
        }
      }

      // Location ist normalerweise in der Zeile darunter (3-Zeichen Kürzel)
      const locationMatch = nextLine.match(/^([A-Z]{2,4})$/);
      if (locationMatch) {
        location = locationMatch[1];
      }
      continue;
    }

    // Alternative: Location direkt unter "Week Planning"
    if (line.toLowerCase().includes('week planning') && !location) {
      const locationMatch = nextLine.match(/^([A-Z]{2,4})$/);
      if (locationMatch) {
        location = locationMatch[1];
      }
    }

    // Expected delta in neg. hour balances
    if (line.toLowerCase().includes('expected delta') ||
        line.toLowerCase().includes('neg. hour balances')) {
      const deltaMatch = findNumberInNextLines(lines, i, 3);
      if (deltaMatch !== null) {
        expectedDelta = deltaMatch;
      }
    }

    // Shifts unable to be assigned
    if (line.toLowerCase().includes('shifts unable') ||
        line.toLowerCase().includes('unable to be assigned')) {
      const shiftsMatch = findNumberInNextLines(lines, i, 3);
      if (shiftsMatch !== null) {
        shiftsUnableToAssign = shiftsMatch;
      }
    }

    // Trips below forecast
    if (line.toLowerCase().includes('trips below') ||
        line.toLowerCase().includes('below forecast')) {
      const tripsMatch = findNumberInNextLines(lines, i, 3);
      if (tripsMatch !== null) {
        tripsBelowForecast = tripsMatch;
      }
    }

    // Runners submitted availabilities
    if (line.toLowerCase().includes('runners submitted') ||
        line.toLowerCase().includes('submitted availabilities')) {
      const runnersMatch = findNumberInNextLines(lines, i, 3);
      if (runnersMatch !== null) {
        runnersSubmittedAvailabilities = runnersMatch;
      }
    }
  }

  // Ergebnis zusammenstellen
  const result = {
    fileName: fileName,
    location: location,
    planningWeek: planningWeek,
    expectedDelta: expectedDelta,
    shiftsUnableToAssign: shiftsUnableToAssign,
    tripsBelowForecast: tripsBelowForecast,
    runnersSubmittedAvailabilities: runnersSubmittedAvailabilities,
    processedDate: new Date()
  };

  console.log('Extrahierte Daten:', result);

  // Nur zurückgeben wenn mindestens Location und Woche gefunden wurden
  if (location && planningWeek) {
    return [result];
  } else {
    console.log(`Unvollständige Daten in ${fileName}: Location=${location}, Week=${planningWeek}`);
    return [];
  }
}

/**
 * Hilfsfunktion: Sucht nach einer Zahl in den nächsten N Zeilen
 */
function findNumberInNextLines(lines, startIndex, maxLines) {
  for (let i = startIndex; i < Math.min(startIndex + maxLines, lines.length); i++) {
    const line = lines[i];

    // Suche nach Zahlen (auch mit Prozentzeichen oder Dezimalstellen)
    const numberMatch = line.match(/(\d+(?:\.\d+)?)\s*%?/);
    if (numberMatch) {
      const number = parseFloat(numberMatch[1]);
      // Ignoriere sehr große Zahlen (wahrscheinlich Jahre oder IDs)
      if (number < 10000) {
        return number;
      }
    }
  }
  return null;
}

/**
 * Bereitet das Ziel-Sheet vor oder erstellt ein neues
 */
function prepareOrCreateTargetSheet() {
  let spreadsheet;

  // Versuche existierendes Sheet zu öffnen
  if (CONFIG.TARGET_SHEET_ID) {
    try {
      spreadsheet = SpreadsheetApp.openById(CONFIG.TARGET_SHEET_ID);
      console.log('Existierendes Sheet gefunden');
    } catch (error) {
      console.log('Sheet ID nicht gefunden, erstelle neues Sheet...');
      spreadsheet = null;
    }
  }

  // Erstelle neues Sheet falls nötig
  if (!spreadsheet) {
    spreadsheet = SpreadsheetApp.create(CONFIG.NEW_SHEET_NAME);
    console.log(`Neues Sheet erstellt: ${spreadsheet.getUrl()}`);
    console.log(`Sheet ID: ${spreadsheet.getId()}`);
  }

  let sheet = spreadsheet.getSheetByName(CONFIG.SHEET_NAME);
  if (!sheet) {
    sheet = spreadsheet.insertSheet(CONFIG.SHEET_NAME);
    // Lösche das Standard-Sheet falls es existiert
    const defaultSheet = spreadsheet.getSheetByName('Sheet1');
    if (defaultSheet && spreadsheet.getSheets().length > 1) {
      spreadsheet.deleteSheet(defaultSheet);
    }
  }

  // Header setzen und formatieren
  setupSheetHeaders(sheet);

  return sheet;
}

/**
 * Richtet die Sheet-Header und Formatierung ein
 */
function setupSheetHeaders(sheet) {
  const headers = [
    'File Name',
    'Location',
    'Planning Week',
    'Expected Delta',
    'Shifts Unable to Assign',
    'Trips Below Forecast',
    'Runners Submitted Availabilities (%)',
    'Processed Date'
  ];

  if (sheet.getLastRow() === 0) {
    // Header setzen
    sheet.getRange(1, 1, 1, headers.length).setValues([headers]);

    // Header formatieren
    const headerRange = sheet.getRange(1, 1, 1, headers.length);
    headerRange.setFontWeight('bold');
    headerRange.setBackground('#4285f4');
    headerRange.setFontColor('white');
    headerRange.setHorizontalAlignment('center');

    // Spaltenbreiten anpassen
    sheet.setColumnWidth(1, 200); // File Name
    sheet.setColumnWidth(2, 80);  // Location
    sheet.setColumnWidth(3, 100); // Planning Week
    sheet.setColumnWidth(4, 120); // Expected Delta
    sheet.setColumnWidth(5, 150); // Shifts Unable
    sheet.setColumnWidth(6, 130); // Trips Below
    sheet.setColumnWidth(7, 180); // Runners Submitted
    sheet.setColumnWidth(8, 150); // Processed Date

    // Zeilen einfrieren
    sheet.setFrozenRows(1);
  }
}

/**
 * Schreibt die extrahierten Daten in das Sheet mit Formatierung
 */
function writeDataToSheet(sheet, data) {
  if (data.length === 0) return;

  const startRow = sheet.getLastRow() + 1;
  const rows = data.map(item => [
    item.fileName,
    item.location,
    item.planningWeek,
    item.expectedDelta,
    item.shiftsUnableToAssign,
    item.tripsBelowForecast,
    item.runnersSubmittedAvailabilities,
    item.processedDate
  ]);

  // Daten einfügen
  const dataRange = sheet.getRange(startRow, 1, rows.length, rows[0].length);
  dataRange.setValues(rows);

  // Formatierung anwenden
  formatDataRows(sheet, startRow, rows.length);

  console.log(`${rows.length} Zeilen in Sheet geschrieben`);
}

/**
 * Formatiert die Datenzeilen
 */
function formatDataRows(sheet, startRow, numRows) {
  // Abwechselnde Zeilenhintergründe
  for (let i = 0; i < numRows; i++) {
    const row = startRow + i;
    const rowRange = sheet.getRange(row, 1, 1, 8);

    if (i % 2 === 0) {
      rowRange.setBackground('#f8f9fa'); // Hellgrau für gerade Zeilen
    }
  }

  // Zahlenformatierung
  if (numRows > 0) {
    // Planning Week als Zahl
    sheet.getRange(startRow, 3, numRows, 1).setNumberFormat('0');

    // Expected Delta mit Vorzeichen
    sheet.getRange(startRow, 4, numRows, 1).setNumberFormat('+0;-0;0');

    // Andere Zahlen als normale Zahlen
    sheet.getRange(startRow, 5, numRows, 2).setNumberFormat('0');

    // Runners Submitted als Prozent (falls > 1) oder Zahl
    const runnersRange = sheet.getRange(startRow, 7, numRows, 1);
    runnersRange.setNumberFormat('0"%"');

    // Datum formatieren
    sheet.getRange(startRow, 8, numRows, 1).setNumberFormat('dd.mm.yyyy hh:mm');
  }

  // Bedingte Formatierung für Expected Delta
  addConditionalFormatting(sheet, startRow, numRows);
}

/**
 * Fügt bedingte Formatierung hinzu
 */
function addConditionalFormatting(sheet, startRow, numRows) {
  if (numRows === 0) return;

  // Expected Delta Spalte (Spalte 4)
  const deltaRange = sheet.getRange(startRow, 4, numRows, 1);

  // Regel für negative Werte (rot)
  const negativeRule = SpreadsheetApp.newConditionalFormatRule()
    .whenNumberLessThan(0)
    .setBackground('#ffebee')
    .setFontColor('#c62828')
    .setRanges([deltaRange])
    .build();

  // Regel für positive Werte (grün)
  const positiveRule = SpreadsheetApp.newConditionalFormatRule()
    .whenNumberGreaterThan(0)
    .setBackground('#e8f5e8')
    .setFontColor('#2e7d32')
    .setRanges([deltaRange])
    .build();

  // Regeln anwenden
  const rules = sheet.getConditionalFormatRules();
  rules.push(negativeRule, positiveRule);
  sheet.setConditionalFormatRules(rules);
}

/**
 * Test-Funktion für einzelne Datei
 */
function testSingleFile() {
  // Ersetze mit einer spezifischen Datei-ID zum Testen
  const fileId = 'DEINE_TEST_DATEI_ID';
  const file = DriveApp.getFileById(fileId);
  
  const extractedData = processScreenshot(file);
  console.log('Extrahierte Daten:', extractedData);
}
